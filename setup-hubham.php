<?php
// إعداد جداول HubHam في قاعدة البيانات الموجودة
require_once 'config/database.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إعداد HubHam</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            direction: rtl;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            max-width: 800px;
            width: 90%;
        }
        .logo { font-size: 48px; text-align: center; margin-bottom: 20px; }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .status { padding: 15px; border-radius: 10px; margin: 10px 0; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .btn { display: inline-block; background: linear-gradient(45deg, #667eea, #764ba2); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 10px; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='logo'>💎</div>
        <h1>إعداد جداول HubHam</h1>";

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<div class='status success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    // إنشاء جداول HubHam
    $hubham_tables = [
        // جدول المنشورات
        "CREATE TABLE IF NOT EXISTS posts (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            content TEXT NOT NULL,
            media_type ENUM('none', 'image', 'video') DEFAULT 'none',
            media_url VARCHAR(500),
            media_thumbnail VARCHAR(500),
            symbols JSON,
            location VARCHAR(255),
            privacy ENUM('public', 'followers', 'private') DEFAULT 'public',
            likes_count INT DEFAULT 0,
            comments_count INT DEFAULT 0,
            shares_count INT DEFAULT 0,
            views_count INT DEFAULT 0,
            is_pinned BOOLEAN DEFAULT FALSE,
            is_deleted BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_created_at (created_at),
            INDEX idx_privacy (privacy)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        // جدول التعليقات
        "CREATE TABLE IF NOT EXISTS comments (
            id INT PRIMARY KEY AUTO_INCREMENT,
            post_id INT NOT NULL,
            user_id INT NOT NULL,
            parent_id INT NULL,
            content TEXT NOT NULL,
            likes_count INT DEFAULT 0,
            is_deleted BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_post_id (post_id),
            INDEX idx_user_id (user_id),
            INDEX idx_parent_id (parent_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        // جدول الإعجابات
        "CREATE TABLE IF NOT EXISTS likes (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            post_id INT NULL,
            comment_id INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_post_like (user_id, post_id),
            UNIQUE KEY unique_comment_like (user_id, comment_id),
            INDEX idx_user_id (user_id),
            INDEX idx_post_id (post_id),
            INDEX idx_comment_id (comment_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        // جدول المتابعة
        "CREATE TABLE IF NOT EXISTS follows (
            id INT PRIMARY KEY AUTO_INCREMENT,
            follower_id INT NOT NULL,
            following_id INT NOT NULL,
            status ENUM('pending', 'accepted', 'blocked') DEFAULT 'accepted',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_follow (follower_id, following_id),
            INDEX idx_follower (follower_id),
            INDEX idx_following (following_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        // جدول المحادثات
        "CREATE TABLE IF NOT EXISTS conversations (
            id INT PRIMARY KEY AUTO_INCREMENT,
            type ENUM('private', 'group') DEFAULT 'private',
            name VARCHAR(255),
            description TEXT,
            created_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_created_by (created_by)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        // جدول المشاركين في المحادثات
        "CREATE TABLE IF NOT EXISTS conversation_participants (
            id INT PRIMARY KEY AUTO_INCREMENT,
            conversation_id INT NOT NULL,
            user_id INT NOT NULL,
            role ENUM('member', 'admin') DEFAULT 'member',
            joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_participant (conversation_id, user_id),
            INDEX idx_conversation (conversation_id),
            INDEX idx_user (user_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        // جدول الرسائل (تحديث الجدول الموجود)
        "CREATE TABLE IF NOT EXISTS hubham_messages (
            id INT PRIMARY KEY AUTO_INCREMENT,
            conversation_id INT NOT NULL,
            sender_id INT NOT NULL,
            content TEXT,
            media_type ENUM('none', 'image', 'video', 'file') DEFAULT 'none',
            media_url VARCHAR(500),
            media_name VARCHAR(255),
            is_read BOOLEAN DEFAULT FALSE,
            is_deleted BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_conversation (conversation_id),
            INDEX idx_sender (sender_id),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        // جدول الإشعارات (تحديث الجدول الموجود)
        "CREATE TABLE IF NOT EXISTS hubham_notifications (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            type ENUM('like', 'comment', 'follow', 'message', 'mention', 'system') NOT NULL,
            title VARCHAR(255) NOT NULL,
            content TEXT,
            related_id INT,
            related_type ENUM('post', 'comment', 'user', 'message'),
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_type (type),
            INDEX idx_is_read (is_read)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        // جدول الحفظ
        "CREATE TABLE IF NOT EXISTS bookmarks (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            post_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_bookmark (user_id, post_id),
            INDEX idx_user_id (user_id),
            INDEX idx_post_id (post_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        // جدول الحظر
        "CREATE TABLE IF NOT EXISTS blocks (
            id INT PRIMARY KEY AUTO_INCREMENT,
            blocker_id INT NOT NULL,
            blocked_id INT NOT NULL,
            reason VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_block (blocker_id, blocked_id),
            INDEX idx_blocker (blocker_id),
            INDEX idx_blocked (blocked_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        // جدول التقارير (تحديث الجدول الموجود)
        "CREATE TABLE IF NOT EXISTS hubham_reports (
            id INT PRIMARY KEY AUTO_INCREMENT,
            reporter_id INT NOT NULL,
            reported_user_id INT,
            reported_post_id INT,
            reported_comment_id INT,
            type ENUM('spam', 'harassment', 'hate_speech', 'violence', 'inappropriate_content', 'fake_account', 'other') NOT NULL,
            reason TEXT NOT NULL,
            status ENUM('pending', 'reviewed', 'resolved', 'dismissed') DEFAULT 'pending',
            admin_notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_reporter (reporter_id),
            INDEX idx_reported_user (reported_user_id),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        // جدول الترندات
        "CREATE TABLE IF NOT EXISTS trending (
            id INT PRIMARY KEY AUTO_INCREMENT,
            symbol VARCHAR(50) NOT NULL,
            post_count INT DEFAULT 0,
            engagement_score FLOAT DEFAULT 0,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_symbol (symbol),
            INDEX idx_engagement (engagement_score),
            INDEX idx_last_updated (last_updated)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        // جدول سجل الأنشطة
        "CREATE TABLE IF NOT EXISTS activity_logs (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT,
            action VARCHAR(100) NOT NULL,
            description TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_action (action),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        // جدول الجلسات
        "CREATE TABLE IF NOT EXISTS sessions (
            id VARCHAR(128) PRIMARY KEY,
            user_id INT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_last_activity (last_activity)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",
        
        // جدول إعدادات النظام
        "CREATE TABLE IF NOT EXISTS system_settings (
            id INT PRIMARY KEY AUTO_INCREMENT,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            description TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_setting_key (setting_key)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
    ];
    
    $created_tables = 0;
    foreach ($hubham_tables as $sql) {
        try {
            $pdo->exec($sql);
            $created_tables++;
        } catch (Exception $e) {
            echo "<div class='status warning'>⚠️ خطأ في إنشاء جدول: " . $e->getMessage() . "</div>";
        }
    }
    
    echo "<div class='status success'>✅ تم إنشاء/تحديث $created_tables جدول لـ HubHam</div>";
    
    // إضافة أعمدة مطلوبة لجدول المستخدمين الموجود
    try {
        $pdo->exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS display_name VARCHAR(100) AFTER username");
        $pdo->exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS bio TEXT AFTER display_name");
        $pdo->exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS profile_image VARCHAR(500) AFTER bio");
        $pdo->exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS cover_image VARCHAR(500) AFTER profile_image");
        $pdo->exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS location VARCHAR(255) AFTER cover_image");
        $pdo->exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS birth_date DATE AFTER location");
        $pdo->exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS phone VARCHAR(20) AFTER birth_date");
        $pdo->exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS role ENUM('user', 'moderator', 'admin') DEFAULT 'user' AFTER phone");
        $pdo->exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS status ENUM('active', 'pending', 'suspended', 'banned') DEFAULT 'active' AFTER role");
        $pdo->exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT FALSE AFTER status");
        $pdo->exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS email_verification_token VARCHAR(255) AFTER email_verified");
        $pdo->exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS email_changes_count INT DEFAULT 0 AFTER email_verification_token");
        $pdo->exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login TIMESTAMP NULL AFTER email_changes_count");
        $pdo->exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS login_attempts INT DEFAULT 0 AFTER last_login");
        $pdo->exec("ALTER TABLE users ADD COLUMN IF NOT EXISTS locked_until TIMESTAMP NULL AFTER login_attempts");
        
        echo "<div class='status success'>✅ تم تحديث جدول المستخدمين بالأعمدة المطلوبة</div>";
    } catch (Exception $e) {
        echo "<div class='status warning'>⚠️ بعض أعمدة المستخدمين موجودة مسبقاً</div>";
    }
    
    // إنشاء مستخدم إداري لـ HubHam إذا لم يكن موجوداً
    try {
        $admin_check = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = 'hubham_admin' OR email = '<EMAIL>'");
        $admin_check->execute();
        
        if ($admin_check->fetchColumn() == 0) {
            $admin_password = password_hash('HubHam@2024', PASSWORD_ARGON2ID);
            
            $stmt = $pdo->prepare("INSERT INTO users (username, email, password, display_name, role, status, email_verified) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([
                'hubham_admin',
                '<EMAIL>',
                $admin_password,
                'مدير HubHam',
                'admin',
                'active',
                1
            ]);
            
            echo "<div class='status success'>✅ تم إنشاء مستخدم إداري جديد لـ HubHam</div>";
            echo "<div class='status warning'>
                <strong>بيانات الدخول:</strong><br>
                اسم المستخدم: hubham_admin<br>
                البريد الإلكتروني: <EMAIL><br>
                كلمة المرور: HubHam@2024
            </div>";
        } else {
            echo "<div class='status warning'>⚠️ يوجد مستخدم إداري مسبقاً</div>";
        }
    } catch (Exception $e) {
        echo "<div class='status error'>❌ خطأ في إنشاء المستخدم الإداري: " . $e->getMessage() . "</div>";
    }
    
    echo "<div class='status success'>
        <h3>🎉 تم إعداد HubHam بنجاح!</h3>
        <p>يمكنك الآن استخدام المنصة بجميع ميزاتها</p>
    </div>";
    
    echo "<div style='text-align: center; margin-top: 30px;'>
        <a href='index.php' class='btn'>الذهاب للصفحة الرئيسية</a>
        <a href='admin/dashboard.php' class='btn'>لوحة تحكم الإدارة</a>
        <a href='test-all.php' class='btn'>اختبار شامل</a>
    </div>";
    
} catch (PDOException $e) {
    echo "<div class='status error'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</div>";
    echo "<div class='status warning'>
        <h3>تحقق من:</h3>
        <ul>
            <li>صحة بيانات الاتصال في config/database.php</li>
            <li>أن الخادم يعمل</li>
            <li>أن قاعدة البيانات موجودة</li>
            <li>صحة اسم المستخدم وكلمة المرور</li>
        </ul>
    </div>";
}

echo "</div></body></html>";
?>
