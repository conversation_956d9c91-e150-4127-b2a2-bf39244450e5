<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// إعادة توجيه المستخدمين المسجلين
if (isset($_SESSION['user_id'])) {
    header('Location: ../index.php');
    exit();
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitizeInput($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    if (empty($email) || empty($password)) {
        $error = 'يرجى ملء جميع الحقول';
    } elseif (!validateEmail($email)) {
        $error = 'البريد الإلكتروني غير صحيح';
    } else {
        // التحقق من محاولات تسجيل الدخول
        $user = getUserByEmail($email);
        
        if ($user) {
            // التحقق من القفل
            if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
                $remaining = ceil((strtotime($user['locked_until']) - time()) / 60);
                $error = "الحساب مقفل لمدة $remaining دقيقة";
            } elseif ($user['status'] === 'banned') {
                $error = 'تم حظر هذا الحساب';
            } elseif ($user['status'] === 'suspended') {
                $error = 'تم تعليق هذا الحساب مؤقتاً';
            } elseif (!$user['email_verified']) {
                $error = 'يرجى تأكيد البريد الإلكتروني أولاً';
            } elseif (verifyPassword($password, $user['password_hash'])) {
                // تسجيل دخول ناجح
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['role'] = $user['role'];
                
                // إعادة تعيين محاولات تسجيل الدخول
                $sql = "UPDATE users SET login_attempts = 0, locked_until = NULL, last_login = NOW() WHERE id = ?";
                executeQuery($sql, [$user['id']]);
                
                // إنشاء جلسة آمنة
                $session_id = generateSecureToken();
                $sql = "INSERT INTO sessions (id, user_id, ip_address, user_agent) VALUES (?, ?, ?, ?)";
                executeQuery($sql, [
                    $session_id,
                    $user['id'],
                    $_SERVER['REMOTE_ADDR'],
                    $_SERVER['HTTP_USER_AGENT'] ?? ''
                ]);
                
                $_SESSION['session_id'] = $session_id;
                
                // تذكر المستخدم
                if ($remember) {
                    $token = generateSecureToken();
                    setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', true, true);
                    
                    // حفظ الرمز في قاعدة البيانات (يمكن إضافة جدول للرموز)
                }
                
                logActivity($user['id'], 'user_login', 'Successful login');
                
                // إعادة التوجيه
                $redirect = $_GET['redirect'] ?? '../index.php';
                header('Location: ' . $redirect);
                exit();
            } else {
                // كلمة مرور خاطئة
                $attempts = $user['login_attempts'] + 1;
                $locked_until = null;
                
                if ($attempts >= 5) {
                    $locked_until = date('Y-m-d H:i:s', time() + (15 * 60)); // قفل لمدة 15 دقيقة
                    $error = 'تم قفل الحساب لمدة 15 دقيقة بسبب المحاولات المتكررة';
                } else {
                    $remaining = 5 - $attempts;
                    $error = "كلمة المرور غير صحيحة. المحاولات المتبقية: $remaining";
                }
                
                $sql = "UPDATE users SET login_attempts = ?, locked_until = ? WHERE id = ?";
                executeQuery($sql, [$attempts, $locked_until, $user['id']]);
                
                logActivity($user['id'], 'failed_login', "Failed login attempt #$attempts");
            }
        } else {
            $error = 'البريد الإلكتروني غير مسجل';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - HubHam</title>
    <link rel="stylesheet" href="../assets/css/auth.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <div class="logo">
                    <i class="fas fa-heart"></i>
                    <h1>HubHam</h1>
                </div>
                <p>مرحباً بك مرة أخرى</p>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo $success; ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" class="auth-form">
                <div class="form-group">
                    <label for="email">البريد الإلكتروني</label>
                    <div class="input-group">
                        <i class="fas fa-envelope"></i>
                        <input type="email" id="email" name="email" required 
                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                               placeholder="أدخل بريدك الإلكتروني">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" required 
                               placeholder="أدخل كلمة المرور">
                        <button type="button" class="toggle-password" onclick="togglePassword('password')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <div class="form-options">
                    <label class="checkbox-label">
                        <input type="checkbox" name="remember">
                        <span class="checkmark"></span>
                        تذكرني
                    </label>
                    
                    <a href="forgot-password.php" class="forgot-link">نسيت كلمة المرور؟</a>
                </div>
                
                <button type="submit" class="auth-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
            </form>
            
            <div class="auth-footer">
                <p>ليس لديك حساب؟ <a href="register.php">إنشاء حساب جديد</a></p>
            </div>
        </div>
        
        <div class="auth-background">
            <div class="floating-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
                <div class="shape shape-4"></div>
                <div class="shape shape-5"></div>
            </div>
        </div>
    </div>
    
    <script src="../assets/js/auth.js"></script>
</body>
</html>
