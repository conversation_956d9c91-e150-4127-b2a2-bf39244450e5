<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit();
}

$user = getUserById($_SESSION['user_id']);
if (!$user || !hasPermission($user, 'manage_users')) {
    header('Location: ../index.php');
    exit();
}

// معالجة الطلبات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $user_id = intval($_POST['user_id'] ?? 0);
    
    switch ($action) {
        case 'suspend':
            suspendUser($user_id);
            $message = 'تم إيقاف المستخدم بنجاح';
            break;
        case 'activate':
            activateUser($user_id);
            $message = 'تم تفعيل المستخدم بنجاح';
            break;
        case 'ban':
            banUser($user_id);
            $message = 'تم حظر المستخدم بنجاح';
            break;
        case 'promote':
            promoteUser($user_id, $_POST['role']);
            $message = 'تم ترقية المستخدم بنجاح';
            break;
    }
}

// جلب المستخدمين
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? '';
$role = $_GET['role'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

$users = getUsers($search, $status, $role, $limit, $offset);
$total_users = getTotalUsers($search, $status, $role);
$total_pages = ceil($total_users / $limit);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - HubHam</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="admin-body">
    <!-- الشريط الجانبي -->
    <?php include 'sidebar.php'; ?>
    
    <!-- المحتوى الرئيسي -->
    <main class="admin-main">
        <!-- الشريط العلوي -->
        <header class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>إدارة المستخدمين</h1>
            </div>
            
            <div class="header-right">
                <div class="admin-user">
                    <img src="<?php echo $user['profile_image'] ?: '../assets/images/default-avatar.png'; ?>" alt="صورة المدير">
                    <div class="user-info">
                        <span class="user-name"><?php echo htmlspecialchars($user['display_name']); ?></span>
                        <span class="user-role"><?php echo $user['role'] === 'admin' ? 'مدير عام' : 'مراقب'; ?></span>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- المحتوى -->
        <div class="admin-content">
            <?php if (isset($message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>
            
            <!-- أدوات البحث والفلترة -->
            <div class="filters-section">
                <form method="GET" class="filters-form">
                    <div class="filter-group">
                        <label for="search">البحث:</label>
                        <input type="text" id="search" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="اسم المستخدم أو البريد الإلكتروني">
                    </div>
                    
                    <div class="filter-group">
                        <label for="status">الحالة:</label>
                        <select id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>نشط</option>
                            <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>معلق</option>
                            <option value="suspended" <?php echo $status === 'suspended' ? 'selected' : ''; ?>>موقوف</option>
                            <option value="banned" <?php echo $status === 'banned' ? 'selected' : ''; ?>>محظور</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="role">الدور:</label>
                        <select id="role" name="role">
                            <option value="">جميع الأدوار</option>
                            <option value="user" <?php echo $role === 'user' ? 'selected' : ''; ?>>مستخدم</option>
                            <option value="moderator" <?php echo $role === 'moderator' ? 'selected' : ''; ?>>مراقب</option>
                            <option value="admin" <?php echo $role === 'admin' ? 'selected' : ''; ?>>مدير</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                        بحث
                    </button>
                    
                    <a href="users.php" class="btn btn-secondary">
                        <i class="fas fa-refresh"></i>
                        إعادة تعيين
                    </a>
                </form>
            </div>
            
            <!-- جدول المستخدمين -->
            <div class="table-card">
                <div class="card-header">
                    <h3>المستخدمون (<?php echo number_format($total_users); ?>)</h3>
                    <button class="btn btn-primary" onclick="exportUsers()">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                </div>
                
                <div class="table-container">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الدور</th>
                                <th>الحالة</th>
                                <th>تاريخ التسجيل</th>
                                <th>آخر نشاط</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user_item): ?>
                            <tr>
                                <td>
                                    <div class="user-cell">
                                        <img src="<?php echo $user_item['profile_image'] ?: '../assets/images/default-avatar.png'; ?>" alt="صورة المستخدم">
                                        <div>
                                            <div class="user-name"><?php echo htmlspecialchars($user_item['display_name']); ?></div>
                                            <div class="username">@<?php echo htmlspecialchars($user_item['username']); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td><?php echo htmlspecialchars($user_item['email']); ?></td>
                                <td>
                                    <span class="role-badge <?php echo $user_item['role']; ?>">
                                        <?php echo getRoleText($user_item['role']); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="status-badge <?php echo $user_item['status']; ?>">
                                        <?php echo getStatusText($user_item['status']); ?>
                                    </span>
                                </td>
                                <td><?php echo date('Y/m/d', strtotime($user_item['created_at'])); ?></td>
                                <td class="real-time" data-timestamp="<?php echo $user_item['last_activity']; ?>">
                                    <?php echo $user_item['last_activity'] ? date('Y/m/d H:i', strtotime($user_item['last_activity'])) : 'لم يسجل دخول'; ?>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-action view" onclick="viewUser(<?php echo $user_item['id']; ?>)" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        
                                        <?php if ($user_item['status'] === 'active'): ?>
                                            <button class="btn-action suspend" onclick="suspendUser(<?php echo $user_item['id']; ?>)" title="إيقاف">
                                                <i class="fas fa-pause"></i>
                                            </button>
                                        <?php elseif ($user_item['status'] === 'suspended'): ?>
                                            <button class="btn-action activate" onclick="activateUser(<?php echo $user_item['id']; ?>)" title="تفعيل">
                                                <i class="fas fa-play"></i>
                                            </button>
                                        <?php endif; ?>
                                        
                                        <?php if ($user_item['status'] !== 'banned'): ?>
                                            <button class="btn-action ban" onclick="banUser(<?php echo $user_item['id']; ?>)" title="حظر">
                                                <i class="fas fa-ban"></i>
                                            </button>
                                        <?php endif; ?>
                                        
                                        <?php if ($user['role'] === 'admin' && $user_item['role'] !== 'admin'): ?>
                                            <button class="btn-action promote" onclick="promoteUser(<?php echo $user_item['id']; ?>)" title="ترقية">
                                                <i class="fas fa-arrow-up"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- التنقل بين الصفحات -->
                <?php if ($total_pages > 1): ?>
                <div class="pagination">
                    <?php if ($page > 1): ?>
                        <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo $status; ?>&role=<?php echo $role; ?>" class="page-btn">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo $status; ?>&role=<?php echo $role; ?>" 
                           class="page-btn <?php echo $i === $page ? 'active' : ''; ?>">
                            <?php echo $i; ?>
                        </a>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo $status; ?>&role=<?php echo $role; ?>" class="page-btn">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </main>
    
    <!-- نافذة تفاصيل المستخدم -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تفاصيل المستخدم</h3>
                <button class="modal-close" onclick="closeModal('userModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="userModalBody">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
        </div>
    </div>
    
    <!-- نافذة ترقية المستخدم -->
    <div id="promoteModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>ترقية المستخدم</h3>
                <button class="modal-close" onclick="closeModal('promoteModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="promoteForm" method="POST">
                    <input type="hidden" name="action" value="promote">
                    <input type="hidden" name="user_id" id="promoteUserId">
                    
                    <div class="form-group">
                        <label for="newRole">الدور الجديد:</label>
                        <select id="newRole" name="role" required>
                            <option value="moderator">مراقب</option>
                            <option value="admin">مدير</option>
                        </select>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">ترقية</button>
                        <button type="button" class="btn btn-secondary" onclick="closeModal('promoteModal')">إلغاء</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="../assets/js/admin.js"></script>
    <script src="../assets/js/users-admin.js"></script>
</body>
</html>

<?php
// دوال مساعدة
function getUsers($search, $status, $role, $limit, $offset) {
    $conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $conditions[] = "(username LIKE ? OR display_name LIKE ? OR email LIKE ?)";
        $search_param = "%$search%";
        $params[] = $search_param;
        $params[] = $search_param;
        $params[] = $search_param;
    }
    
    if (!empty($status)) {
        $conditions[] = "status = ?";
        $params[] = $status;
    }
    
    if (!empty($role)) {
        $conditions[] = "role = ?";
        $params[] = $role;
    }
    
    $where_clause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";
    
    $sql = "SELECT *, 
                   (SELECT MAX(created_at) FROM activity_logs WHERE user_id = users.id) as last_activity
            FROM users 
            $where_clause 
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?";
    
    $params[] = $limit;
    $params[] = $offset;
    
    return fetchAll($sql, $params);
}

function getTotalUsers($search, $status, $role) {
    $conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $conditions[] = "(username LIKE ? OR display_name LIKE ? OR email LIKE ?)";
        $search_param = "%$search%";
        $params[] = $search_param;
        $params[] = $search_param;
        $params[] = $search_param;
    }
    
    if (!empty($status)) {
        $conditions[] = "status = ?";
        $params[] = $status;
    }
    
    if (!empty($role)) {
        $conditions[] = "role = ?";
        $params[] = $role;
    }
    
    $where_clause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";
    
    $sql = "SELECT COUNT(*) as count FROM users $where_clause";
    
    return fetchOne($sql, $params)['count'];
}

function getRoleText($role) {
    $roles = [
        'user' => 'مستخدم',
        'moderator' => 'مراقب',
        'admin' => 'مدير'
    ];
    return $roles[$role] ?? $role;
}
?>
