<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: auth/login.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$user = getUserById($user_id);

if (!$user) {
    session_destroy();
    header('Location: auth/login.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HubHam - حبهم</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <h1><i class="fas fa-heart"></i> HubHam</h1>
            </div>
            
            <div class="nav-search">
                <div class="search-box">
                    <input type="text" id="searchInput" placeholder="البحث في HubHam...">
                    <button type="button" id="searchBtn"><i class="fas fa-search"></i></button>
                </div>
            </div>
            
            <div class="nav-menu">
                <a href="index.php" class="nav-link active"><i class="fas fa-home"></i> الرئيسية</a>
                <a href="explore.php" class="nav-link"><i class="fas fa-compass"></i> استكشف</a>
                <a href="trending.php" class="nav-link"><i class="fas fa-fire"></i> الشائع</a>
                <a href="messages.php" class="nav-link"><i class="fas fa-envelope"></i> الرسائل</a>
                
                <div class="user-menu">
                    <div class="user-avatar" onclick="toggleUserMenu()">
                        <img src="<?php echo $user['profile_image'] ?: 'assets/images/default-avatar.png'; ?>" alt="صورة المستخدم">
                        <?php if ($user['role'] === 'admin'): ?>
                            <span class="admin-badge"><i class="fas fa-crown"></i></span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="user-dropdown" id="userDropdown">
                        <a href="profile.php?user=<?php echo $user['username']; ?>"><i class="fas fa-user"></i> الملف الشخصي</a>
                        <a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a>
                        <?php if ($user['role'] === 'admin' || $user['role'] === 'moderator'): ?>
                            <a href="admin/dashboard.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a>
                        <?php endif; ?>
                        <hr>
                        <a href="auth/logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="container">
            <div class="content-grid">
                <!-- الشريط الجانبي الأيسر -->
                <aside class="sidebar-left">
                    <div class="sidebar-section">
                        <h3>الاختصارات</h3>
                        <ul class="sidebar-menu">
                            <li><a href="profile.php?user=<?php echo $user['username']; ?>"><i class="fas fa-user"></i> ملفي الشخصي</a></li>
                            <li><a href="bookmarks.php"><i class="fas fa-bookmark"></i> المحفوظات</a></li>
                            <li><a href="lists.php"><i class="fas fa-list"></i> القوائم</a></li>
                            <li><a href="moments.php"><i class="fas fa-clock"></i> اللحظات</a></li>
                        </ul>
                    </div>
                    
                    <div class="sidebar-section">
                        <h3>الاهتمامات</h3>
                        <div class="interests-tags" id="userInterests">
                            <!-- سيتم تحميل الاهتمامات ديناميكياً -->
                        </div>
                    </div>
                </aside>

                <!-- المحتوى المركزي -->
                <section class="main-feed">
                    <!-- نموذج إنشاء منشور جديد -->
                    <div class="post-composer">
                        <div class="composer-header">
                            <img src="<?php echo $user['profile_image'] ?: 'assets/images/default-avatar.png'; ?>" alt="صورة المستخدم" class="composer-avatar">
                            <textarea placeholder="ما الذي يحدث؟" id="postContent" maxlength="280"></textarea>
                        </div>
                        
                        <div class="composer-tools">
                            <div class="media-tools">
                                <button type="button" class="tool-btn" onclick="attachImage()"><i class="fas fa-image"></i></button>
                                <button type="button" class="tool-btn" onclick="attachVideo()"><i class="fas fa-video"></i></button>
                                <button type="button" class="tool-btn" onclick="addSymbol()"><i class="fas fa-hashtag"></i></button>
                                <button type="button" class="tool-btn" onclick="addLocation()"><i class="fas fa-map-marker-alt"></i></button>
                            </div>
                            
                            <div class="post-actions">
                                <span class="char-count" id="charCount">280</span>
                                <button type="button" class="post-btn" id="publishBtn" onclick="publishPost()">نشر</button>
                            </div>
                        </div>
                        
                        <input type="file" id="imageInput" accept="image/*" style="display: none;" onchange="handleImageUpload(this)">
                        <input type="file" id="videoInput" accept="video/*" style="display: none;" onchange="handleVideoUpload(this)">
                    </div>

                    <!-- خلاصة المنشورات -->
                    <div class="feed-container" id="feedContainer">
                        <!-- سيتم تحميل المنشورات ديناميكياً -->
                        <div class="loading-spinner">
                            <i class="fas fa-spinner fa-spin"></i>
                            <p>جاري تحميل المنشورات...</p>
                        </div>
                    </div>
                </section>

                <!-- الشريط الجانبي الأيمن -->
                <aside class="sidebar-right">
                    <!-- الترندات -->
                    <div class="sidebar-section">
                        <h3>الشائع الآن</h3>
                        <div class="trending-list" id="trendingList">
                            <!-- سيتم تحميل الترندات ديناميكياً -->
                        </div>
                    </div>
                    
                    <!-- اقتراحات المتابعة -->
                    <div class="sidebar-section">
                        <h3>من تتابع</h3>
                        <div class="suggestions-list" id="suggestionsList">
                            <!-- سيتم تحميل الاقتراحات ديناميكياً -->
                        </div>
                    </div>
                    
                    <!-- إحصائيات المستخدم -->
                    <div class="sidebar-section">
                        <h3>إحصائياتك</h3>
                        <div class="user-stats">
                            <div class="stat-item">
                                <span class="stat-number" id="postsCount">0</span>
                                <span class="stat-label">منشور</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" id="followersCount">0</span>
                                <span class="stat-label">متابع</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" id="followingCount">0</span>
                                <span class="stat-label">متابَع</span>
                            </div>
                        </div>
                    </div>
                </aside>
            </div>
        </div>
    </main>

    <!-- نافذة منبثقة للرموز -->
    <div class="modal" id="symbolModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>اختر رمزاً</h3>
                <button class="close-btn" onclick="closeModal('symbolModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="symbols-grid">
                    <button class="symbol-btn" onclick="insertSymbol('♥')">♥ حب</button>
                    <button class="symbol-btn" onclick="insertSymbol('★')">★ نجمة</button>
                    <button class="symbol-btn" onclick="insertSymbol('♦')">♦ ماسة</button>
                    <button class="symbol-btn" onclick="insertSymbol('♠')">♠ بستوني</button>
                    <button class="symbol-btn" onclick="insertSymbol('♣')">♣ ترف</button>
                    <button class="symbol-btn" onclick="insertSymbol('☀')">☀ شمس</button>
                    <button class="symbol-btn" onclick="insertSymbol('☽')">☽ قمر</button>
                    <button class="symbol-btn" onclick="insertSymbol('⚡')">⚡ برق</button>
                    <button class="symbol-btn" onclick="insertSymbol('🔥')">🔥 نار</button>
                    <button class="symbol-btn" onclick="insertSymbol('💎')">💎 جوهرة</button>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/main.js"></script>
    <script src="assets/js/posts.js"></script>
    <script src="assets/js/search.js"></script>
</body>
</html>
