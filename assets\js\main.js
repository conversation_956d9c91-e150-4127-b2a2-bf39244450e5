// متغيرات عامة
let currentUser = null;
let isLoading = false;
let feedOffset = 0;
const POSTS_PER_PAGE = 20;

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    loadUserData();
    loadFeed();
    loadTrending();
    loadUserStats();
    setupEventListeners();
    setupInfiniteScroll();
});

// تهيئة التطبيق
function initializeApp() {
    // إعداد CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (csrfToken) {
        window.csrfToken = csrfToken.getAttribute('content');
    }
    
    // إعداد axios defaults
    if (typeof axios !== 'undefined') {
        axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
        if (window.csrfToken) {
            axios.defaults.headers.common['X-CSRF-TOKEN'] = window.csrfToken;
        }
    }
}

// تحميل بيانات المستخدم
async function loadUserData() {
    try {
        const response = await fetch('api/user/profile.php');
        const data = await response.json();
        
        if (data.success) {
            currentUser = data.user;
            updateUserInterface();
        }
    } catch (error) {
        console.error('Error loading user data:', error);
    }
}

// تحديث واجهة المستخدم
function updateUserInterface() {
    if (!currentUser) return;
    
    // تحديث صورة المستخدم
    const avatars = document.querySelectorAll('.user-avatar img, .composer-avatar');
    avatars.forEach(avatar => {
        if (avatar.tagName === 'IMG') {
            avatar.src = currentUser.profile_image || 'assets/images/default-avatar.png';
        }
    });
    
    // تحديث شارة الإدارة
    if (currentUser.role === 'admin') {
        const adminBadges = document.querySelectorAll('.admin-badge');
        adminBadges.forEach(badge => badge.style.display = 'flex');
    }
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // قائمة المستخدم المنسدلة
    const userAvatar = document.querySelector('.user-avatar');
    const userDropdown = document.getElementById('userDropdown');
    
    if (userAvatar && userDropdown) {
        userAvatar.addEventListener('click', function(e) {
            e.stopPropagation();
            toggleUserMenu();
        });
        
        document.addEventListener('click', function() {
            userDropdown.classList.remove('show');
        });
    }
    
    // البحث
    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.getElementById('searchBtn');
    
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
        
        // البحث المباشر
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (this.value.length >= 2) {
                    performLiveSearch(this.value);
                }
            }, 300);
        });
    }
    
    if (searchBtn) {
        searchBtn.addEventListener('click', performSearch);
    }
    
    // عداد الأحرف في منطقة إنشاء المنشور
    const postContent = document.getElementById('postContent');
    const charCount = document.getElementById('charCount');
    const publishBtn = document.getElementById('publishBtn');
    
    if (postContent && charCount) {
        postContent.addEventListener('input', function() {
            const remaining = 280 - this.value.length;
            charCount.textContent = remaining;
            charCount.style.color = remaining < 20 ? '#e74c3c' : '#657786';
            
            if (publishBtn) {
                publishBtn.disabled = remaining < 0 || this.value.trim().length === 0;
            }
        });
    }
    
    // إغلاق النوافذ المنبثقة بالضغط على Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeAllModals();
        }
    });
    
    // منع إرسال النماذج الفارغة
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('error');
                } else {
                    field.classList.remove('error');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
            }
        });
    });
}

// تبديل قائمة المستخدم
function toggleUserMenu() {
    const dropdown = document.getElementById('userDropdown');
    if (dropdown) {
        dropdown.classList.toggle('show');
    }
}

// تحميل الخلاصة
async function loadFeed(reset = false) {
    if (isLoading) return;
    
    isLoading = true;
    const feedContainer = document.getElementById('feedContainer');
    
    if (reset) {
        feedOffset = 0;
        feedContainer.innerHTML = '<div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i><p>جاري تحميل المنشورات...</p></div>';
    }
    
    try {
        const response = await fetch(`api/posts/feed.php?offset=${feedOffset}&limit=${POSTS_PER_PAGE}`);
        const data = await response.json();
        
        if (data.success) {
            if (reset) {
                feedContainer.innerHTML = '';
            } else {
                // إزالة مؤشر التحميل
                const loadingSpinner = feedContainer.querySelector('.loading-spinner');
                if (loadingSpinner) {
                    loadingSpinner.remove();
                }
            }
            
            if (data.posts.length === 0 && feedOffset === 0) {
                feedContainer.innerHTML = '<div class="empty-feed"><p>لا توجد منشورات لعرضها</p></div>';
            } else {
                data.posts.forEach(post => {
                    feedContainer.appendChild(createPostElement(post));
                });
                feedOffset += data.posts.length;
            }
        } else {
            showNotification(data.message || 'حدث خطأ في تحميل المنشورات', 'error');
        }
    } catch (error) {
        console.error('Error loading feed:', error);
        showNotification('حدث خطأ في الاتصال', 'error');
    } finally {
        isLoading = false;
    }
}

// إنشاء عنصر منشور
function createPostElement(post) {
    const postDiv = document.createElement('div');
    postDiv.className = 'post-item fade-in-up';
    postDiv.dataset.postId = post.id;
    
    const timeAgo = formatTimeAgo(post.created_at);
    const isLiked = post.user_liked > 0;
    
    postDiv.innerHTML = `
        <div class="post-header">
            <img src="${post.profile_image || 'assets/images/default-avatar.png'}" alt="صورة المستخدم" class="post-avatar">
            <div class="post-user-info">
                <div class="post-user-name">
                    ${post.display_name}
                    ${post.role === 'admin' ? '<i class="fas fa-crown admin-badge" title="مدير"></i>' : ''}
                    ${post.role === 'moderator' ? '<i class="fas fa-shield-alt moderator-badge" title="مراقب"></i>' : ''}
                </div>
                <div class="post-username">@${post.username}</div>
            </div>
            <div class="post-time">${timeAgo}</div>
        </div>
        
        <div class="post-content">${formatPostContent(post.content)}</div>
        
        ${post.media_url ? createMediaElement(post.media_type, post.media_url) : ''}
        
        <div class="post-actions">
            <button class="action-btn comment-btn" onclick="showComments(${post.id})">
                <i class="fas fa-comment"></i>
                <span>${post.comments_count || 0}</span>
            </button>
            
            <button class="action-btn like-btn ${isLiked ? 'liked' : ''}" onclick="toggleLike(${post.id})">
                <i class="fas fa-heart"></i>
                <span>${post.likes_count || 0}</span>
            </button>
            
            <button class="action-btn share-btn" onclick="sharePost(${post.id})">
                <i class="fas fa-share"></i>
                <span>مشاركة</span>
            </button>
            
            <button class="action-btn bookmark-btn" onclick="toggleBookmark(${post.id})">
                <i class="fas fa-bookmark"></i>
            </button>
        </div>
    `;
    
    return postDiv;
}

// تنسيق محتوى المنشور
function formatPostContent(content) {
    // تحويل الروابط
    content = content.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank" rel="noopener">$1</a>');
    
    // تحويل الإشارات
    content = content.replace(/@(\w+)/g, '<a href="profile.php?user=$1" class="mention">@$1</a>');
    
    // تحويل الرموز المخصصة
    content = content.replace(/([♥★♦♠♣☀☽⚡🔥💎])/g, '<span class="custom-symbol">$1</span>');
    
    return content;
}

// إنشاء عنصر الوسائط
function createMediaElement(type, url) {
    if (type === 'image') {
        return `<div class="post-media"><img src="${url}" alt="صورة" loading="lazy" onclick="openImageModal('${url}')"></div>`;
    } else if (type === 'video') {
        return `<div class="post-media"><video controls preload="metadata"><source src="${url}" type="video/mp4">متصفحك لا يدعم تشغيل الفيديو</video></div>`;
    }
    return '';
}

// تنسيق الوقت
function formatTimeAgo(datetime) {
    const now = new Date();
    const time = new Date(datetime);
    const diff = Math.floor((now - time) / 1000);
    
    if (diff < 60) return 'الآن';
    if (diff < 3600) return Math.floor(diff / 60) + ' د';
    if (diff < 86400) return Math.floor(diff / 3600) + ' س';
    if (diff < 2592000) return Math.floor(diff / 86400) + ' ي';
    if (diff < 31536000) return Math.floor(diff / 2592000) + ' ش';
    
    return Math.floor(diff / 31536000) + ' سنة';
}

// إعداد التمرير اللانهائي
function setupInfiniteScroll() {
    let ticking = false;
    
    window.addEventListener('scroll', function() {
        if (!ticking) {
            requestAnimationFrame(function() {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const windowHeight = window.innerHeight;
                const documentHeight = document.documentElement.scrollHeight;
                
                if (scrollTop + windowHeight >= documentHeight - 1000) {
                    loadFeed();
                }
                
                ticking = false;
            });
            ticking = true;
        }
    });
}

// تحميل الترندات
async function loadTrending() {
    try {
        const response = await fetch('api/trending.php');
        const data = await response.json();
        
        if (data.success) {
            const trendingList = document.getElementById('trendingList');
            if (trendingList) {
                trendingList.innerHTML = '';
                
                data.trending.forEach(trend => {
                    const trendDiv = document.createElement('div');
                    trendDiv.className = 'trending-item';
                    trendDiv.innerHTML = `
                        <div class="trending-symbol">${trend.symbol}</div>
                        <div class="trending-count">${trend.posts_count} منشور</div>
                    `;
                    trendDiv.addEventListener('click', () => searchBySymbol(trend.symbol));
                    trendingList.appendChild(trendDiv);
                });
            }
        }
    } catch (error) {
        console.error('Error loading trending:', error);
    }
}

// تحميل إحصائيات المستخدم
async function loadUserStats() {
    try {
        const response = await fetch('api/user/stats.php');
        const data = await response.json();
        
        if (data.success) {
            const stats = data.stats;
            
            const postsCount = document.getElementById('postsCount');
            const followersCount = document.getElementById('followersCount');
            const followingCount = document.getElementById('followingCount');
            
            if (postsCount) postsCount.textContent = stats.posts_count || 0;
            if (followersCount) followersCount.textContent = stats.followers_count || 0;
            if (followingCount) followingCount.textContent = stats.following_count || 0;
        }
    } catch (error) {
        console.error('Error loading user stats:', error);
    }
}

// البحث
function performSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput && searchInput.value.trim()) {
        window.location.href = `search.php?q=${encodeURIComponent(searchInput.value.trim())}`;
    }
}

// البحث المباشر
async function performLiveSearch(query) {
    try {
        const response = await fetch(`api/search/live.php?q=${encodeURIComponent(query)}`);
        const data = await response.json();
        
        if (data.success) {
            showSearchSuggestions(data.results);
        }
    } catch (error) {
        console.error('Error performing live search:', error);
    }
}

// عرض اقتراحات البحث
function showSearchSuggestions(results) {
    // إزالة الاقتراحات السابقة
    const existingSuggestions = document.querySelector('.search-suggestions');
    if (existingSuggestions) {
        existingSuggestions.remove();
    }
    
    if (results.length === 0) return;
    
    const searchBox = document.querySelector('.search-box');
    const suggestionsDiv = document.createElement('div');
    suggestionsDiv.className = 'search-suggestions';
    
    results.forEach(result => {
        const suggestionDiv = document.createElement('div');
        suggestionDiv.className = 'search-suggestion';
        suggestionDiv.innerHTML = `
            <img src="${result.profile_image || 'assets/images/default-avatar.png'}" alt="">
            <div>
                <div class="suggestion-name">${result.display_name}</div>
                <div class="suggestion-username">@${result.username}</div>
            </div>
        `;
        suggestionDiv.addEventListener('click', () => {
            window.location.href = `profile.php?user=${result.username}`;
        });
        suggestionsDiv.appendChild(suggestionDiv);
    });
    
    searchBox.appendChild(suggestionsDiv);
    
    // إزالة الاقتراحات عند النقر خارجها
    setTimeout(() => {
        document.addEventListener('click', function removeSuggestions(e) {
            if (!searchBox.contains(e.target)) {
                suggestionsDiv.remove();
                document.removeEventListener('click', removeSuggestions);
            }
        });
    }, 100);
}

// البحث بالرمز
function searchBySymbol(symbol) {
    window.location.href = `search.php?symbol=${encodeURIComponent(symbol)}`;
}

// إغلاق جميع النوافذ المنبثقة
function closeAllModals() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.classList.remove('show');
    });
}

// إغلاق نافذة منبثقة محددة
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');
    }
}

// عرض إشعار
function showNotification(message, type = 'info') {
    // إزالة الإشعارات السابقة
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    document.body.appendChild(notification);
    
    // إزالة الإشعار تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// تحديث الصفحة بسلاسة
function refreshFeed() {
    loadFeed(true);
    loadTrending();
    loadUserStats();
}

// تحسين الأداء - تأجيل تحميل الصور
function lazyLoadImages() {
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// تهيئة تحميل الصور المؤجل
document.addEventListener('DOMContentLoaded', lazyLoadImages);

// إعادة تهيئة تحميل الصور عند إضافة محتوى جديد
function reinitializeLazyLoading() {
    lazyLoadImages();
}

// تصدير الدوال للاستخدام في ملفات أخرى
window.HubHam = {
    loadFeed,
    refreshFeed,
    showNotification,
    closeModal,
    formatTimeAgo,
    reinitializeLazyLoading
};
