/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* شريط التنقل */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-brand h1 {
    color: #667eea;
    font-size: 28px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
}

.nav-brand i {
    color: #e74c3c;
    animation: heartbeat 2s infinite;
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.nav-search {
    flex: 1;
    max-width: 400px;
    margin: 0 30px;
}

.search-box {
    position: relative;
    display: flex;
}

.search-box input {
    width: 100%;
    padding: 12px 50px 12px 20px;
    border: 2px solid #e1e8ed;
    border-radius: 25px;
    font-size: 16px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-box button {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #657786;
    font-size: 18px;
    cursor: pointer;
    transition: color 0.3s ease;
}

.search-box button:hover {
    color: #667eea;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 20px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    text-decoration: none;
    color: #657786;
    border-radius: 20px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-link:hover, .nav-link.active {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.user-menu {
    position: relative;
}

.user-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    cursor: pointer;
    position: relative;
    border: 3px solid transparent;
    transition: all 0.3s ease;
}

.user-avatar:hover {
    border-color: #667eea;
    transform: scale(1.05);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.admin-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    border: 2px solid white;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-dropdown a {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    text-decoration: none;
    color: #333;
    transition: background 0.3s ease;
}

.user-dropdown a:hover {
    background: #f8f9fa;
}

.user-dropdown hr {
    border: none;
    height: 1px;
    background: #e1e8ed;
    margin: 5px 0;
}

/* المحتوى الرئيسي */
.main-content {
    margin-top: 80px;
    padding: 20px 0;
}

.content-grid {
    display: grid;
    grid-template-columns: 250px 1fr 300px;
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

/* الأشرطة الجانبية */
.sidebar-left, .sidebar-right {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 20px;
    height: fit-content;
    backdrop-filter: blur(10px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.sidebar-section {
    margin-bottom: 25px;
}

.sidebar-section:last-child {
    margin-bottom: 0;
}

.sidebar-section h3 {
    color: #333;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e1e8ed;
}

.sidebar-menu {
    list-style: none;
}

.sidebar-menu li {
    margin-bottom: 8px;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 15px;
    text-decoration: none;
    color: #657786;
    border-radius: 15px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.sidebar-menu a:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    transform: translateX(-5px);
}

/* منطقة إنشاء المنشور */
.post-composer {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 25px;
    backdrop-filter: blur(10px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.composer-header {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.composer-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #e1e8ed;
}

.composer-header textarea {
    flex: 1;
    border: none;
    outline: none;
    resize: none;
    font-size: 18px;
    font-family: inherit;
    background: transparent;
    min-height: 60px;
    padding: 10px 0;
    color: #333;
}

.composer-header textarea::placeholder {
    color: #657786;
}

.composer-tools {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid #e1e8ed;
}

.media-tools {
    display: flex;
    gap: 15px;
}

.tool-btn {
    background: none;
    border: none;
    color: #667eea;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.tool-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: scale(1.1);
}

.post-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.char-count {
    color: #657786;
    font-size: 14px;
    font-weight: 500;
}

.post-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.post-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.post-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* خلاصة المنشورات */
.feed-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.post-item {
    padding: 25px;
    border-bottom: 1px solid #e1e8ed;
    transition: background 0.3s ease;
}

.post-item:hover {
    background: rgba(102, 126, 234, 0.02);
}

.post-item:last-child {
    border-bottom: none;
}

.post-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.post-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e1e8ed;
}

.post-user-info {
    flex: 1;
}

.post-user-name {
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.post-username {
    color: #657786;
    font-size: 14px;
}

.post-time {
    color: #657786;
    font-size: 14px;
}

.post-content {
    margin-bottom: 15px;
    line-height: 1.6;
    font-size: 16px;
    color: #333;
}

.post-media {
    margin-bottom: 15px;
    border-radius: 15px;
    overflow: hidden;
}

.post-media img, .post-media video {
    width: 100%;
    height: auto;
    display: block;
}

.post-actions {
    display: flex;
    justify-content: space-around;
    padding-top: 15px;
    border-top: 1px solid #e1e8ed;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    color: #657786;
    cursor: pointer;
    padding: 8px 15px;
    border-radius: 15px;
    transition: all 0.3s ease;
    font-size: 14px;
}

.action-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.action-btn.liked {
    color: #e74c3c;
}

.action-btn.liked:hover {
    background: rgba(231, 76, 60, 0.1);
}

/* الترندات */
.trending-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.trending-item {
    padding: 12px 15px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.trending-item:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateX(-3px);
}

.trending-symbol {
    font-weight: 600;
    color: #667eea;
    margin-bottom: 5px;
}

.trending-count {
    font-size: 12px;
    color: #657786;
}

/* الإحصائيات */
.user-stats {
    display: flex;
    justify-content: space-around;
    text-align: center;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.stat-number {
    font-size: 20px;
    font-weight: 700;
    color: #667eea;
}

.stat-label {
    font-size: 12px;
    color: #657786;
}

/* النوافذ المنبثقة */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: 20px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e1e8ed;
}

.modal-header h3 {
    color: #333;
    font-size: 20px;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    color: #657786;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.modal-body {
    padding: 25px;
}

.symbols-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.symbol-btn {
    padding: 15px;
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
}

.symbol-btn:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
    transform: translateY(-2px);
}

/* مؤشر التحميل */
.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px;
    color: #657786;
}

.loading-spinner i {
    font-size: 30px;
    margin-bottom: 15px;
    color: #667eea;
}

/* الاهتمامات */
.interests-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.interest-tag {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.interest-tag:hover {
    background: rgba(102, 126, 234, 0.2);
    transform: scale(1.05);
}

/* تأثيرات الحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease forwards;
}

/* تحسينات الأداء */
.post-item, .sidebar-section, .post-composer {
    will-change: transform;
}

/* تأثيرات التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
}
