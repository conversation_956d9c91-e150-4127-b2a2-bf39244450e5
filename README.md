# HubHam - منصة التواصل الاجتماعي

## نظرة عامة
HubHam (حبهم) هي منصة تواصل اجتماعي متكاملة مصممة خصيصاً للمجتمع العربي. تتميز بتصميم جميل وفريد مع ميزات متقدمة للتفاعل والمشاركة.

## الميزات الرئيسية

### 🔐 نظام المصادقة والأمان
- تسجيل دخول آمن بالبريد الإلكتروني وكلمة المرور
- كلمات مرور قوية (8 أحرف، حرف كبير، صغير، رقم، رمز)
- أسماء مستخدمين تبدأ بحرف إنجليزي
- تشفير متقدم للبيانات الحساسة
- حماية من محاولات الاختراق

### 👥 إدارة المستخدمين
- أدوار متعددة: مستخدم، مراقب، إداري
- صلاحيات متدرجة للإدارة
- إمكانية تعيين مراقبين وإداريين
- تتبع تاريخ تغيير البريد الإلكتروني (حد أقصى 3 مرات)
- ملفات شخصية مخصصة

### 📝 نظام المنشورات
- منشورات نصية بحد أقصى 280 حرف
- رموز مخصصة بدلاً من الهاشتاغ (♥★♦♠♣☀☽⚡🔥💎)
- مشاركة الصور والفيديوهات
- تحديد الموقع الجغرافي
- إعجابات وتعليقات ومشاركة

### 🔥 النظام الذكي للترندات
- عرض أشهر 10 منشورات ديناميكياً
- تحديث كل 6 ساعات
- خوارزمية ذكية لحساب الشعبية
- عرض متنوع حسب المنطقة والاهتمامات

### 💬 الرسائل الخاصة
- محادثات خاصة بين المستخدمين
- مشاركة الصور والفيديوهات والملفات
- تشفير الرسائل
- إشعارات فورية

### 🎯 التوصيات الذكية
- عرض المحتوى حسب الموقع الجغرافي
- توصيات مخصصة لكل مستخدم
- تنويع المحتوى ديناميكياً
- عدم تكرار المحتوى للمستخدمين

### 📱 التصميم المتجاوب
- متوافق مع جميع الأجهزة
- تصميم مُحسن للهواتف الذكية
- دعم iPhone و Galaxy و اللابتوب
- واجهة سلسة وسريعة

## متطلبات النظام

### الخادم
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache أو Nginx
- مساحة تخزين كافية للملفات المرفوعة

### المتصفح
- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+

## التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone https://github.com/your-repo/hubham.git
cd hubham
```

### 2. إعداد قاعدة البيانات
1. قم بإنشاء قاعدة بيانات MySQL جديدة
2. عدّل إعدادات قاعدة البيانات في `config/database.php`
3. قم بزيارة `http://localhost/hubham/setup.php` لإعداد الجداول

### 3. تشغيل الخادم المحلي
```bash
# على Windows
start-server.bat

# على Linux/Mac
chmod +x start-server.sh
./start-server.sh
```

### 4. الوصول للموقع
افتح المتصفح وانتقل إلى: `http://localhost:3322`

## بيانات الدخول الافتراضية

### المدير العام
- **اسم المستخدم:** admin
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** Admin@123

## هيكل المشروع

```
hubham/
├── assets/                 # الملفات الثابتة
│   ├── css/               # ملفات التصميم
│   ├── js/                # ملفات JavaScript
│   └── images/            # الصور
├── auth/                  # نظام المصادقة
├── api/                   # واجهات برمجة التطبيقات
├── admin/                 # لوحة تحكم الإدارة
├── config/                # ملفات الإعداد
├── database/              # قاعدة البيانات
├── includes/              # الملفات المشتركة
├── uploads/               # الملفات المرفوعة
└── logs/                  # ملفات السجلات
```

## الأمان والحماية

### تشفير البيانات
- تشفير كلمات المرور بـ Argon2ID
- تشفير البيانات الحساسة بـ AES-256
- رموز أمان عشوائية آمنة

### حماية من الهجمات
- حماية من SQL Injection
- حماية من XSS
- حماية من CSRF
- تحديد معدل الطلبات
- قفل الحسابات بعد محاولات فاشلة

### خصوصية البيانات
- إعدادات خصوصية مرنة
- تشفير الرسائل الخاصة
- حذف آمن للبيانات
- سجلات مراجعة شاملة

## الميزات المتقدمة

### البحث الذكي
- بحث في المستخدمين والمنشورات
- اقتراحات فورية
- فلترة حسب النوع والموقع

### الإشعارات
- إشعارات فورية للتفاعلات
- إشعارات الرسائل الجديدة
- إشعارات المتابعة
- إعدادات إشعارات مخصصة

### التحليلات
- إحصائيات المستخدمين
- تحليل المحتوى الشائع
- تقارير النشاط
- مراقبة الأداء

## الدعم والمساعدة

### التوثيق
- دليل المستخدم الشامل
- دليل المطور
- أمثلة على الاستخدام
- أسئلة شائعة

### الدعم التقني
- نظام تذاكر الدعم
- منتدى المجتمع
- دردشة مباشرة
- بريد إلكتروني للدعم

## الترخيص
هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## المساهمة
نرحب بمساهماتكم! يرجى قراءة دليل المساهمة قبل إرسال طلبات السحب.

## الإصدارات المستقبلية
- تطبيق الهاتف المحمول
- دعم البث المباشر
- متجر الملصقات والرموز
- نظام النقاط والمكافآت
- تكامل مع منصات أخرى

---

**HubHam** - حيث تلتقي القلوب وتتواصل الأرواح 💎
