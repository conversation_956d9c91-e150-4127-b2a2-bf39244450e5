/* لوحة تحكم الإدارة - HubHam */

/* التخطيط العام */
body.admin-body {
    margin: 0;
    padding: 0;
    font-family: 'Cairo', sans-serif;
    background: #f8f9fa;
    display: flex;
    min-height: 100vh;
    direction: rtl;
}

/* الشريط الجانبي */
.admin-sidebar {
    width: 280px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: fixed;
    height: 100vh;
    right: 0;
    top: 0;
    z-index: 1000;
    transition: transform 0.3s ease;
    overflow-y: auto;
}

.sidebar-header {
    padding: 30px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header .logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sidebar-header .logo i {
    font-size: 32px;
    color: #ffd700;
}

.sidebar-header .logo h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
}

.sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 20px 0;
}

.sidebar-nav li {
    margin: 5px 0;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.sidebar-nav a:hover,
.sidebar-nav li.active a {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(-5px);
}

.sidebar-nav a i {
    font-size: 18px;
    width: 25px;
    margin-left: 15px;
}

.sidebar-nav .badge {
    background: #ff4757;
    color: white;
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 12px;
    margin-right: auto;
}

.sidebar-footer {
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    padding: 0 20px;
}

.back-to-site {
    display: flex;
    align-items: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    text-decoration: none;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.back-to-site:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.back-to-site i {
    margin-left: 10px;
}

/* المحتوى الرئيسي */
.admin-main {
    flex: 1;
    margin-right: 280px;
    min-height: 100vh;
    background: #f8f9fa;
}

.admin-header {
    background: white;
    padding: 20px 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 20px;
    color: #333;
    cursor: pointer;
    padding: 10px;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.sidebar-toggle:hover {
    background: #f0f0f0;
}

.admin-header h1 {
    margin: 0;
    color: #333;
    font-size: 28px;
    font-weight: 700;
}

.admin-user {
    display: flex;
    align-items: center;
    gap: 15px;
}

.admin-user img {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #667eea;
}

.user-info {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    color: #333;
    font-size: 16px;
}

.user-role {
    font-size: 14px;
    color: #666;
}

/* المحتوى */
.admin-content {
    padding: 30px;
}

/* بطاقات الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.stat-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 70px;
    height: 70px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    color: white;
}

.stat-icon.users {
    background: linear-gradient(45deg, #667eea, #764ba2);
}

.stat-icon.posts {
    background: linear-gradient(45deg, #f093fb, #f5576c);
}

.stat-icon.reports {
    background: linear-gradient(45deg, #ffecd2, #fcb69f);
}

.stat-icon.active {
    background: linear-gradient(45deg, #a8edea, #fed6e3);
}

.stat-info h3 {
    margin: 0 0 5px 0;
    font-size: 32px;
    font-weight: 700;
    color: #333;
}

.stat-info p {
    margin: 0 0 10px 0;
    color: #666;
    font-size: 16px;
}

.stat-change {
    font-size: 14px;
    padding: 5px 10px;
    border-radius: 20px;
    font-weight: 600;
}

.stat-change.positive {
    background: #d4edda;
    color: #155724;
}

.stat-change.negative {
    background: #f8d7da;
    color: #721c24;
}

.stat-change.neutral {
    background: #e2e3e5;
    color: #383d41;
}

/* الرسوم البيانية */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.chart-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.card-header {
    padding: 25px 30px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    color: #333;
    font-size: 20px;
    font-weight: 600;
}

.card-header select {
    padding: 8px 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: white;
    color: #333;
    font-size: 14px;
}

.chart-container {
    padding: 30px;
    height: 300px;
}

/* الجداول */
.tables-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 25px;
}

.table-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.view-all {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    transition: color 0.3s ease;
}

.view-all:hover {
    color: #764ba2;
}

.table-container {
    overflow-x: auto;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
}

.admin-table th {
    background: #f8f9fa;
    padding: 15px;
    text-align: right;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #eee;
}

.admin-table td {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
}

.user-cell {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-cell img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.user-name {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.username {
    color: #666;
    font-size: 13px;
}

.status-badge {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.status-badge.pending {
    background: #fff3cd;
    color: #856404;
}

.status-badge.suspended {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.banned {
    background: #f5c6cb;
    color: #721c24;
}

.action-buttons {
    display: flex;
    gap: 8px;
}

.btn-action {
    width: 35px;
    height: 35px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-action.view {
    background: #e3f2fd;
    color: #1976d2;
}

.btn-action.edit {
    background: #fff3e0;
    color: #f57c00;
}

.btn-action.approve {
    background: #e8f5e8;
    color: #2e7d32;
}

.btn-action.reject {
    background: #ffebee;
    color: #d32f2f;
}

.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.report-type {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.report-reason {
    max-width: 200px;
    color: #666;
    font-size: 14px;
}

/* التجاوب */
@media (max-width: 1024px) {
    .admin-sidebar {
        transform: translateX(100%);
    }
    
    .admin-sidebar.open {
        transform: translateX(0);
    }
    
    .admin-main {
        margin-right: 0;
    }
    
    .sidebar-toggle {
        display: block;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .tables-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .admin-content {
        padding: 20px;
    }
    
    .admin-header {
        padding: 15px 20px;
    }
    
    .admin-header h1 {
        font-size: 24px;
    }
    
    .stat-card {
        padding: 20px;
        flex-direction: column;
        text-align: center;
    }
    
    .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }
    
    .admin-table {
        font-size: 14px;
    }
    
    .admin-table th,
    .admin-table td {
        padding: 10px;
    }
}

@media (max-width: 480px) {
    .admin-sidebar {
        width: 100%;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .user-cell {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }
    
    .action-buttons {
        justify-content: center;
    }
}
