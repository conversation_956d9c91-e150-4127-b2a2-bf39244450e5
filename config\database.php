<?php
// إعدادات قاعدة البيانات
define('DB_HOST', 'srv1513.hstgr.io');
define('DB_NAME', 'u302460181_hoasb');
define('DB_USER', 'u302460181_hoasb');
define('DB_PASS', '10743211uU@');
define('DB_CHARSET', 'utf8mb4');

// إعدادات الأمان
define('ENCRYPTION_KEY', 'HubHam2024SecureKey!@#$%^&*()');
define('SESSION_LIFETIME', 3600 * 24 * 7); // أسبوع واحد

// إعدادات التطبيق
define('SITE_URL', 'http://localhost:3322');
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 50 * 1024 * 1024); // 50 ميجابايت
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_VIDEO_TYPES', ['mp4', 'webm', 'ogg', 'avi', 'mov']);

// إعدادات المحتوى
define('MAX_POST_LENGTH', 280);
define('MAX_USERNAME_LENGTH', 50);
define('MIN_PASSWORD_LENGTH', 8);
define('MAX_EMAIL_CHANGES', 3);

// إعدادات الترندات
define('TRENDING_REFRESH_HOURS', 6);
define('TOP_TRENDING_COUNT', 10);

class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ];
            
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            die("خطأ في الاتصال بقاعدة البيانات");
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    // منع الاستنساخ
    private function __clone() {}
    
    // منع إلغاء التسلسل
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

// دالة للحصول على اتصال قاعدة البيانات
function getDB() {
    return Database::getInstance()->getConnection();
}

// دالة لتنفيذ استعلام آمن
function executeQuery($sql, $params = []) {
    try {
        $db = getDB();
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch (PDOException $e) {
        error_log("Query execution failed: " . $e->getMessage());
        return false;
    }
}

// دالة للحصول على صف واحد
function fetchOne($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->fetch() : false;
}

// دالة للحصول على عدة صفوف
function fetchAll($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->fetchAll() : false;
}

// دالة للحصول على آخر معرف مدرج
function getLastInsertId() {
    return getDB()->lastInsertId();
}

// دالة لبدء معاملة
function beginTransaction() {
    return getDB()->beginTransaction();
}

// دالة لتأكيد المعاملة
function commit() {
    return getDB()->commit();
}

// دالة لإلغاء المعاملة
function rollback() {
    return getDB()->rollBack();
}

// دالة لتشفير كلمة المرور
function hashPassword($password) {
    return password_hash($password, PASSWORD_ARGON2ID, [
        'memory_cost' => 65536,
        'time_cost' => 4,
        'threads' => 3
    ]);
}

// دالة للتحقق من كلمة المرور
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// دالة لتوليد رمز عشوائي آمن
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length));
}

// دالة لتشفير البيانات الحساسة
function encryptData($data) {
    $key = hash('sha256', ENCRYPTION_KEY, true);
    $iv = random_bytes(16);
    $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
    return base64_encode($iv . $encrypted);
}

// دالة لفك تشفير البيانات
function decryptData($encryptedData) {
    $key = hash('sha256', ENCRYPTION_KEY, true);
    $data = base64_decode($encryptedData);
    $iv = substr($data, 0, 16);
    $encrypted = substr($data, 16);
    return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
}

// دالة لتنظيف المدخلات
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// دالة للتحقق من صحة البريد الإلكتروني
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// دالة للتحقق من قوة كلمة المرور
function validatePassword($password) {
    // يجب أن تحتوي على 8 أحرف على الأقل، حرف كبير، حرف صغير، رقم، ورمز
    $pattern = '/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/';
    return preg_match($pattern, $password);
}

// دالة للتحقق من صحة اسم المستخدم
function validateUsername($username) {
    // يجب أن يبدأ بحرف إنجليزي ويحتوي على أحرف وأرقام فقط
    $pattern = '/^[a-zA-Z][a-zA-Z0-9_]{0,49}$/';
    return preg_match($pattern, $username);
}

// دالة لتسجيل الأخطاء
function logError($message, $file = '', $line = '') {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] Error: $message";
    if ($file) $logMessage .= " in $file";
    if ($line) $logMessage .= " on line $line";
    error_log($logMessage . PHP_EOL, 3, 'logs/error.log');
}

// دالة لتسجيل الأنشطة
function logActivity($user_id, $action, $details = '') {
    $sql = "INSERT INTO activity_logs (user_id, action, details, ip_address, user_agent, created_at) 
            VALUES (?, ?, ?, ?, ?, NOW())";
    $params = [
        $user_id,
        $action,
        $details,
        $_SERVER['REMOTE_ADDR'] ?? '',
        $_SERVER['HTTP_USER_AGENT'] ?? ''
    ];
    executeQuery($sql, $params);
}

// إنشاء مجلدات الرفع إذا لم تكن موجودة
$uploadDirs = [
    'uploads/images',
    'uploads/videos',
    'uploads/avatars',
    'uploads/temp',
    'logs'
];

foreach ($uploadDirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// إعداد معالج الأخطاء المخصص
set_error_handler(function($severity, $message, $file, $line) {
    logError($message, $file, $line);
});

// إعداد معالج الاستثناءات المخصص
set_exception_handler(function($exception) {
    logError($exception->getMessage(), $exception->getFile(), $exception->getLine());
});
?>
