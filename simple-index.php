<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// تحميل الإعدادات
require_once 'config/database.php';

// اختبار الاتصال
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    $db_status = "متصل";
    $db_color = "green";
} catch (Exception $e) {
    $db_status = "غير متصل: " . $e->getMessage();
    $db_color = "red";
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HubHam - منصة التواصل الاجتماعي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .logo {
            font-size: 64px;
            margin-bottom: 20px;
        }
        
        h1 {
            font-size: 48px;
            color: #333;
            margin-bottom: 10px;
        }
        
        .subtitle {
            font-size: 20px;
            color: #666;
            margin-bottom: 30px;
        }
        
        .status {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .status h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .btn {
            display: inline-block;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 25px;
            margin: 10px;
            transition: transform 0.3s ease;
            font-weight: bold;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        
        .feature-card h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .feature-card p {
            color: #666;
            line-height: 1.6;
        }
        
        .footer {
            text-align: center;
            margin-top: 50px;
            color: rgba(255, 255, 255, 0.8);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">💎</div>
            <h1>HubHam</h1>
            <p class="subtitle">منصة التواصل الاجتماعي الجديدة</p>
            
            <div style="margin: 20px 0;">
                <a href="auth/login.php" class="btn">تسجيل الدخول</a>
                <a href="auth/register.php" class="btn btn-secondary">إنشاء حساب</a>
                <a href="admin/dashboard.php" class="btn btn-success">لوحة التحكم</a>
            </div>
        </div>
        
        <div class="status">
            <h3>حالة النظام</h3>
            
            <div class="status-item">
                <span>قاعدة البيانات:</span>
                <span style="color: <?php echo $db_color; ?>; font-weight: bold;">
                    <?php echo $db_status; ?>
                </span>
            </div>
            
            <div class="status-item">
                <span>إصدار PHP:</span>
                <span style="color: green; font-weight: bold;">
                    <?php echo PHP_VERSION; ?>
                </span>
            </div>
            
            <div class="status-item">
                <span>الخادم:</span>
                <span style="color: green; font-weight: bold;">
                    يعمل على المنفذ 3322
                </span>
            </div>
            
            <div class="status-item">
                <span>الوقت:</span>
                <span style="font-weight: bold;">
                    <?php echo date('Y-m-d H:i:s'); ?>
                </span>
            </div>
        </div>
        
        <div class="features">
            <div class="feature-card">
                <div class="feature-icon">🔐</div>
                <h3>أمان متقدم</h3>
                <p>نظام مصادقة آمن مع تشفير البيانات وحماية من الهجمات</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">💬</div>
                <h3>تواصل ذكي</h3>
                <p>منشورات مع رموز مخصصة ونظام رسائل خاصة متطور</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔥</div>
                <h3>ترندات ديناميكية</h3>
                <p>عرض المحتوى الشائع مع تحديث كل 6 ساعات</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📱</div>
                <h3>تصميم متجاوب</h3>
                <p>يعمل بسلاسة على جميع الأجهزة والمتصفحات</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">👑</div>
                <h3>إدارة متقدمة</h3>
                <p>لوحة تحكم شاملة مع صلاحيات متدرجة</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🌍</div>
                <h3>دعم العربية</h3>
                <p>مصمم خصيصاً للمجتمع العربي مع دعم كامل للغة</p>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 40px;">
            <a href="debug.php" class="btn btn-secondary">تشخيص النظام</a>
            <a href="setup-hubham.php" class="btn btn-success">إعداد قاعدة البيانات</a>
            <a href="test-all.php" class="btn btn-danger">اختبار شامل</a>
        </div>
        
        <div class="footer">
            <p>&copy; 2024 HubHam - جميع الحقوق محفوظة</p>
            <p>تم التطوير بواسطة فريق HubHam</p>
        </div>
    </div>
</body>
</html>
