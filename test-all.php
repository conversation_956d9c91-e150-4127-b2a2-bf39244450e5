<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل - HubHam</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 36px;
        }
        
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
        }
        
        .test-section h2 {
            color: #667eea;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status {
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 25px;
            transition: transform 0.3s ease;
            font-weight: bold;
            display: inline-block;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .link-card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .link-card h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .summary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            margin: 30px 0;
        }
        
        .summary h2 {
            margin: 0 0 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار شامل لمنصة HubHam</h1>
        
        <?php
        require_once 'config/database.php';
        
        $tests = [];
        $total_tests = 0;
        $passed_tests = 0;
        
        // اختبار الاتصال بقاعدة البيانات
        try {
            $pdo = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
                DB_USER,
                DB_PASS,
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
            $tests['database'] = ['status' => 'success', 'message' => 'متصل بنجاح'];
            $passed_tests++;
        } catch (Exception $e) {
            $tests['database'] = ['status' => 'error', 'message' => 'فشل الاتصال: ' . $e->getMessage()];
        }
        $total_tests++;
        
        // اختبار الجداول المطلوبة
        $required_tables = ['users', 'posts', 'comments', 'likes', 'follows', 'messages', 'notifications'];
        $existing_tables = [];
        
        if (isset($pdo)) {
            try {
                $existing_tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
                $missing_tables = array_diff($required_tables, $existing_tables);
                
                if (empty($missing_tables)) {
                    $tests['tables'] = ['status' => 'success', 'message' => 'جميع الجداول موجودة'];
                    $passed_tests++;
                } else {
                    $tests['tables'] = ['status' => 'warning', 'message' => 'جداول مفقودة: ' . implode(', ', $missing_tables)];
                }
            } catch (Exception $e) {
                $tests['tables'] = ['status' => 'error', 'message' => 'خطأ في فحص الجداول'];
            }
        } else {
            $tests['tables'] = ['status' => 'error', 'message' => 'لا يمكن فحص الجداول بدون اتصال'];
        }
        $total_tests++;
        
        // اختبار الملفات المطلوبة
        $required_files = [
            'index.php' => 'الصفحة الرئيسية',
            'auth/login.php' => 'صفحة تسجيل الدخول',
            'auth/register.php' => 'صفحة التسجيل',
            'admin/dashboard.php' => 'لوحة تحكم الإدارة',
            'config/database.php' => 'إعدادات قاعدة البيانات',
            'includes/functions.php' => 'الدوال المساعدة'
        ];
        
        $missing_files = [];
        foreach ($required_files as $file => $description) {
            if (!file_exists($file)) {
                $missing_files[] = "$description ($file)";
            }
        }
        
        if (empty($missing_files)) {
            $tests['files'] = ['status' => 'success', 'message' => 'جميع الملفات موجودة'];
            $passed_tests++;
        } else {
            $tests['files'] = ['status' => 'error', 'message' => 'ملفات مفقودة: ' . implode(', ', $missing_files)];
        }
        $total_tests++;
        
        // اختبار المجلدات المطلوبة
        $required_dirs = ['uploads', 'logs', 'assets/css', 'assets/js', 'api'];
        $missing_dirs = [];
        
        foreach ($required_dirs as $dir) {
            if (!is_dir($dir)) {
                $missing_dirs[] = $dir;
            }
        }
        
        if (empty($missing_dirs)) {
            $tests['directories'] = ['status' => 'success', 'message' => 'جميع المجلدات موجودة'];
            $passed_tests++;
        } else {
            $tests['directories'] = ['status' => 'warning', 'message' => 'مجلدات مفقودة: ' . implode(', ', $missing_dirs)];
        }
        $total_tests++;
        
        // اختبار صلاحيات الكتابة
        $writable_dirs = ['uploads', 'logs'];
        $non_writable = [];
        
        foreach ($writable_dirs as $dir) {
            if (!is_writable($dir) && !mkdir($dir, 0755, true)) {
                $non_writable[] = $dir;
            }
        }
        
        if (empty($non_writable)) {
            $tests['permissions'] = ['status' => 'success', 'message' => 'صلاحيات الكتابة صحيحة'];
            $passed_tests++;
        } else {
            $tests['permissions'] = ['status' => 'error', 'message' => 'مجلدات غير قابلة للكتابة: ' . implode(', ', $non_writable)];
        }
        $total_tests++;
        
        // اختبار إضافات PHP
        $required_extensions = ['pdo', 'pdo_mysql', 'gd', 'json', 'mbstring'];
        $missing_extensions = [];
        
        foreach ($required_extensions as $ext) {
            if (!extension_loaded($ext)) {
                $missing_extensions[] = $ext;
            }
        }
        
        if (empty($missing_extensions)) {
            $tests['php_extensions'] = ['status' => 'success', 'message' => 'جميع الإضافات متوفرة'];
            $passed_tests++;
        } else {
            $tests['php_extensions'] = ['status' => 'error', 'message' => 'إضافات مفقودة: ' . implode(', ', $missing_extensions)];
        }
        $total_tests++;
        ?>
        
        <div class="summary">
            <h2>📊 ملخص الاختبارات</h2>
            <p>نجح <?php echo $passed_tests; ?> من أصل <?php echo $total_tests; ?> اختبار</p>
            <p>نسبة النجاح: <?php echo round(($passed_tests / $total_tests) * 100, 1); ?>%</p>
        </div>
        
        <div class="test-section">
            <h2>🔍 نتائج الاختبارات</h2>
            
            <div class="test-item">
                <span>اتصال قاعدة البيانات</span>
                <span class="status <?php echo $tests['database']['status']; ?>">
                    <?php echo $tests['database']['message']; ?>
                </span>
            </div>
            
            <div class="test-item">
                <span>الجداول المطلوبة</span>
                <span class="status <?php echo $tests['tables']['status']; ?>">
                    <?php echo $tests['tables']['message']; ?>
                </span>
            </div>
            
            <div class="test-item">
                <span>الملفات الأساسية</span>
                <span class="status <?php echo $tests['files']['status']; ?>">
                    <?php echo $tests['files']['message']; ?>
                </span>
            </div>
            
            <div class="test-item">
                <span>المجلدات المطلوبة</span>
                <span class="status <?php echo $tests['directories']['status']; ?>">
                    <?php echo $tests['directories']['message']; ?>
                </span>
            </div>
            
            <div class="test-item">
                <span>صلاحيات الكتابة</span>
                <span class="status <?php echo $tests['permissions']['status']; ?>">
                    <?php echo $tests['permissions']['message']; ?>
                </span>
            </div>
            
            <div class="test-item">
                <span>إضافات PHP</span>
                <span class="status <?php echo $tests['php_extensions']['status']; ?>">
                    <?php echo $tests['php_extensions']['message']; ?>
                </span>
            </div>
        </div>
        
        <?php if (isset($pdo)): ?>
        <div class="test-section">
            <h2>📈 إحصائيات قاعدة البيانات</h2>
            
            <?php
            try {
                $user_count = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
                echo "<div class='test-item'><span>عدد المستخدمين</span><span class='status success'>$user_count</span></div>";
                
                if (in_array('posts', $existing_tables)) {
                    $post_count = $pdo->query("SELECT COUNT(*) FROM posts")->fetchColumn();
                    echo "<div class='test-item'><span>عدد المنشورات</span><span class='status success'>$post_count</span></div>";
                }
                
                echo "<div class='test-item'><span>إجمالي الجداول</span><span class='status success'>" . count($existing_tables) . "</span></div>";
                
            } catch (Exception $e) {
                echo "<div class='test-item'><span>خطأ في الإحصائيات</span><span class='status error'>" . $e->getMessage() . "</span></div>";
            }
            ?>
        </div>
        <?php endif; ?>
        
        <div class="test-section">
            <h2>🔗 روابط الاختبار</h2>
            <div class="links-grid">
                <div class="link-card">
                    <h3>الصفحات الرئيسية</h3>
                    <a href="index.php" class="btn" target="_blank">الصفحة الرئيسية</a>
                    <a href="quick-start.php" class="btn" target="_blank">التشغيل السريع</a>
                </div>
                
                <div class="link-card">
                    <h3>المصادقة</h3>
                    <a href="auth/login.php" class="btn" target="_blank">تسجيل الدخول</a>
                    <a href="auth/register.php" class="btn" target="_blank">التسجيل</a>
                </div>
                
                <div class="link-card">
                    <h3>الإدارة</h3>
                    <a href="admin/dashboard.php" class="btn" target="_blank">لوحة التحكم</a>
                    <a href="admin/users.php" class="btn" target="_blank">إدارة المستخدمين</a>
                </div>
                
                <div class="link-card">
                    <h3>الاختبارات</h3>
                    <a href="test-connection.php" class="btn" target="_blank">اختبار الاتصال</a>
                    <a href="setup.php" class="btn" target="_blank">إعداد قاعدة البيانات</a>
                </div>
            </div>
        </div>
        
        <?php if ($passed_tests === $total_tests): ?>
        <div class="summary">
            <h2>🎉 تهانينا!</h2>
            <p>جميع الاختبارات نجحت! منصة HubHam جاهزة للاستخدام</p>
            <a href="index.php" class="btn">ابدأ الاستخدام الآن</a>
        </div>
        <?php else: ?>
        <div class="test-section">
            <h2>⚠️ إجراءات مطلوبة</h2>
            <p>يرجى إصلاح المشاكل المذكورة أعلاه قبل استخدام المنصة</p>
            <a href="setup.php" class="btn">إعداد قاعدة البيانات</a>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
