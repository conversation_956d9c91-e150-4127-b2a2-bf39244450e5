<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['user_id'])) {
    header('Location: ../auth/login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit();
}

$user = getUserById($_SESSION['user_id']);
if (!$user || !hasPermission($user, 'view_admin_panel')) {
    header('Location: ../index.php');
    exit();
}

// جلب الإحصائيات
$stats = getAdminStats();
$recent_users = getRecentUsers(10);
$recent_posts = getRecentPosts(10);
$pending_reports = getPendingReports(10);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم الإدارة - HubHam</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- الشريط الجانبي -->
    <aside class="admin-sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-crown"></i>
                <h2>لوحة التحكم</h2>
            </div>
        </div>
        
        <nav class="sidebar-nav">
            <ul>
                <li class="active">
                    <a href="dashboard.php">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>الرئيسية</span>
                    </a>
                </li>
                <li>
                    <a href="users.php">
                        <i class="fas fa-users"></i>
                        <span>إدارة المستخدمين</span>
                    </a>
                </li>
                <li>
                    <a href="posts.php">
                        <i class="fas fa-file-alt"></i>
                        <span>إدارة المنشورات</span>
                    </a>
                </li>
                <li>
                    <a href="reports.php">
                        <i class="fas fa-flag"></i>
                        <span>التقارير</span>
                        <?php if ($stats['pending_reports'] > 0): ?>
                            <span class="badge"><?php echo $stats['pending_reports']; ?></span>
                        <?php endif; ?>
                    </a>
                </li>
                <li>
                    <a href="analytics.php">
                        <i class="fas fa-chart-bar"></i>
                        <span>التحليلات</span>
                    </a>
                </li>
                <li>
                    <a href="settings.php">
                        <i class="fas fa-cog"></i>
                        <span>إعدادات النظام</span>
                    </a>
                </li>
                <li>
                    <a href="logs.php">
                        <i class="fas fa-history"></i>
                        <span>سجل الأنشطة</span>
                    </a>
                </li>
            </ul>
        </nav>
        
        <div class="sidebar-footer">
            <a href="../index.php" class="back-to-site">
                <i class="fas fa-arrow-left"></i>
                <span>العودة للموقع</span>
            </a>
        </div>
    </aside>
    
    <!-- المحتوى الرئيسي -->
    <main class="admin-main">
        <!-- الشريط العلوي -->
        <header class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>لوحة تحكم الإدارة</h1>
            </div>
            
            <div class="header-right">
                <div class="admin-user">
                    <img src="<?php echo $user['profile_image'] ?: '../assets/images/default-avatar.png'; ?>" alt="صورة المدير">
                    <div class="user-info">
                        <span class="user-name"><?php echo htmlspecialchars($user['display_name']); ?></span>
                        <span class="user-role"><?php echo $user['role'] === 'admin' ? 'مدير عام' : 'مراقب'; ?></span>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- المحتوى -->
        <div class="admin-content">
            <!-- بطاقات الإحصائيات -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon users">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo number_format($stats['total_users']); ?></h3>
                        <p>إجمالي المستخدمين</p>
                        <span class="stat-change positive">+<?php echo $stats['new_users_today']; ?> اليوم</span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon posts">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo number_format($stats['total_posts']); ?></h3>
                        <p>إجمالي المنشورات</p>
                        <span class="stat-change positive">+<?php echo $stats['new_posts_today']; ?> اليوم</span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon reports">
                        <i class="fas fa-flag"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo number_format($stats['pending_reports']); ?></h3>
                        <p>التقارير المعلقة</p>
                        <span class="stat-change <?php echo $stats['pending_reports'] > 0 ? 'negative' : 'neutral'; ?>">
                            يتطلب مراجعة
                        </span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon active">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo number_format($stats['active_users']); ?></h3>
                        <p>المستخدمون النشطون</p>
                        <span class="stat-change positive">آخر 24 ساعة</span>
                    </div>
                </div>
            </div>
            
            <!-- الرسوم البيانية -->
            <div class="charts-grid">
                <div class="chart-card">
                    <div class="card-header">
                        <h3>نمو المستخدمين</h3>
                        <select id="userGrowthPeriod">
                            <option value="7">آخر 7 أيام</option>
                            <option value="30" selected>آخر 30 يوم</option>
                            <option value="90">آخر 3 أشهر</option>
                        </select>
                    </div>
                    <div class="chart-container">
                        <canvas id="userGrowthChart"></canvas>
                    </div>
                </div>
                
                <div class="chart-card">
                    <div class="card-header">
                        <h3>نشاط المنشورات</h3>
                        <select id="postActivityPeriod">
                            <option value="7">آخر 7 أيام</option>
                            <option value="30" selected>آخر 30 يوم</option>
                        </select>
                    </div>
                    <div class="chart-container">
                        <canvas id="postActivityChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- الجداول -->
            <div class="tables-grid">
                <!-- المستخدمون الجدد -->
                <div class="table-card">
                    <div class="card-header">
                        <h3>المستخدمون الجدد</h3>
                        <a href="users.php" class="view-all">عرض الكل</a>
                    </div>
                    <div class="table-container">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>المستخدم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_users as $recent_user): ?>
                                <tr>
                                    <td>
                                        <div class="user-cell">
                                            <img src="<?php echo $recent_user['profile_image'] ?: '../assets/images/default-avatar.png'; ?>" alt="صورة المستخدم">
                                            <div>
                                                <div class="user-name"><?php echo htmlspecialchars($recent_user['display_name']); ?></div>
                                                <div class="username">@<?php echo htmlspecialchars($recent_user['username']); ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($recent_user['email']); ?></td>
                                    <td><?php echo date('Y/m/d', strtotime($recent_user['created_at'])); ?></td>
                                    <td>
                                        <span class="status-badge <?php echo $recent_user['status']; ?>">
                                            <?php echo getStatusText($recent_user['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-action view" onclick="viewUser(<?php echo $recent_user['id']; ?>)">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn-action edit" onclick="editUser(<?php echo $recent_user['id']; ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- التقارير المعلقة -->
                <div class="table-card">
                    <div class="card-header">
                        <h3>التقارير المعلقة</h3>
                        <a href="reports.php" class="view-all">عرض الكل</a>
                    </div>
                    <div class="table-container">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>المبلغ</th>
                                    <th>النوع</th>
                                    <th>السبب</th>
                                    <th>التاريخ</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($pending_reports as $report): ?>
                                <tr>
                                    <td>
                                        <div class="user-cell">
                                            <img src="<?php echo $report['reporter_image'] ?: '../assets/images/default-avatar.png'; ?>" alt="صورة المبلغ">
                                            <div>
                                                <div class="user-name"><?php echo htmlspecialchars($report['reporter_name']); ?></div>
                                                <div class="username">@<?php echo htmlspecialchars($report['reporter_username']); ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="report-type"><?php echo getReportTypeText($report['type']); ?></span>
                                    </td>
                                    <td class="report-reason"><?php echo htmlspecialchars(substr($report['reason'], 0, 50)) . '...'; ?></td>
                                    <td><?php echo date('Y/m/d', strtotime($report['created_at'])); ?></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-action view" onclick="viewReport(<?php echo $report['id']; ?>)">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn-action approve" onclick="approveReport(<?php echo $report['id']; ?>)">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn-action reject" onclick="rejectReport(<?php echo $report['id']; ?>)">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="../assets/js/admin.js"></script>
</body>
</html>

<?php
// دوال مساعدة
function getAdminStats() {
    $stats = [];
    
    // إجمالي المستخدمين
    $stats['total_users'] = fetchOne("SELECT COUNT(*) as count FROM users")['count'];
    
    // المستخدمون الجدد اليوم
    $stats['new_users_today'] = fetchOne("SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE()")['count'];
    
    // إجمالي المنشورات
    $stats['total_posts'] = fetchOne("SELECT COUNT(*) as count FROM posts WHERE is_deleted = FALSE")['count'];
    
    // المنشورات الجديدة اليوم
    $stats['new_posts_today'] = fetchOne("SELECT COUNT(*) as count FROM posts WHERE DATE(created_at) = CURDATE() AND is_deleted = FALSE")['count'];
    
    // التقارير المعلقة
    $stats['pending_reports'] = fetchOne("SELECT COUNT(*) as count FROM reports WHERE status = 'pending'")['count'];
    
    // المستخدمون النشطون (آخر 24 ساعة)
    $stats['active_users'] = fetchOne("SELECT COUNT(DISTINCT user_id) as count FROM activity_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)")['count'];
    
    return $stats;
}

function getRecentUsers($limit) {
    $sql = "SELECT id, username, display_name, email, profile_image, status, created_at 
            FROM users 
            ORDER BY created_at DESC 
            LIMIT ?";
    return fetchAll($sql, [$limit]);
}

function getRecentPosts($limit) {
    $sql = "SELECT p.*, u.username, u.display_name, u.profile_image 
            FROM posts p 
            JOIN users u ON p.user_id = u.id 
            WHERE p.is_deleted = FALSE 
            ORDER BY p.created_at DESC 
            LIMIT ?";
    return fetchAll($sql, [$limit]);
}

function getPendingReports($limit) {
    $sql = "SELECT r.*, 
                   u1.username as reporter_username, u1.display_name as reporter_name, u1.profile_image as reporter_image,
                   u2.username as reported_username, u2.display_name as reported_name
            FROM reports r 
            JOIN users u1 ON r.reporter_id = u1.id 
            LEFT JOIN users u2 ON r.reported_user_id = u2.id 
            WHERE r.status = 'pending' 
            ORDER BY r.created_at DESC 
            LIMIT ?";
    return fetchAll($sql, [$limit]);
}

function getStatusText($status) {
    $statuses = [
        'active' => 'نشط',
        'pending' => 'معلق',
        'suspended' => 'موقوف',
        'banned' => 'محظور'
    ];
    return $statuses[$status] ?? $status;
}

function getReportTypeText($type) {
    $types = [
        'spam' => 'رسائل مزعجة',
        'harassment' => 'تحرش',
        'hate_speech' => 'خطاب كراهية',
        'violence' => 'عنف',
        'inappropriate_content' => 'محتوى غير مناسب',
        'fake_account' => 'حساب مزيف',
        'other' => 'أخرى'
    ];
    return $types[$type] ?? $type;
}
?>
