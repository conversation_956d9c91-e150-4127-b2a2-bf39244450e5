<?php
// الحصول على الصفحة الحالية
$current_page = basename($_SERVER['PHP_SELF'], '.php');

// جلب إحصائيات سريعة للشريط الجانبي
$pending_reports = fetchOne("SELECT COUNT(*) as count FROM reports WHERE status = 'pending'")['count'];
$new_users_today = fetchOne("SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE()")['count'];
?>

<aside class="admin-sidebar">
    <div class="sidebar-header">
        <div class="logo">
            <i class="fas fa-crown"></i>
            <h2>لوحة التحكم</h2>
        </div>
    </div>
    
    <nav class="sidebar-nav">
        <ul>
            <li class="<?php echo $current_page === 'dashboard' ? 'active' : ''; ?>">
                <a href="dashboard.php">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>الرئيسية</span>
                </a>
            </li>
            
            <li class="<?php echo $current_page === 'users' ? 'active' : ''; ?>">
                <a href="users.php">
                    <i class="fas fa-users"></i>
                    <span>إدارة المستخدمين</span>
                    <?php if ($new_users_today > 0): ?>
                        <span class="badge"><?php echo $new_users_today; ?></span>
                    <?php endif; ?>
                </a>
            </li>
            
            <li class="<?php echo $current_page === 'posts' ? 'active' : ''; ?>">
                <a href="posts.php">
                    <i class="fas fa-file-alt"></i>
                    <span>إدارة المنشورات</span>
                </a>
            </li>
            
            <li class="<?php echo $current_page === 'reports' ? 'active' : ''; ?>">
                <a href="reports.php">
                    <i class="fas fa-flag"></i>
                    <span>التقارير</span>
                    <?php if ($pending_reports > 0): ?>
                        <span class="badge"><?php echo $pending_reports; ?></span>
                    <?php endif; ?>
                </a>
            </li>
            
            <li class="<?php echo $current_page === 'analytics' ? 'active' : ''; ?>">
                <a href="analytics.php">
                    <i class="fas fa-chart-bar"></i>
                    <span>التحليلات</span>
                </a>
            </li>
            
            <li class="<?php echo $current_page === 'messages' ? 'active' : ''; ?>">
                <a href="messages.php">
                    <i class="fas fa-envelope"></i>
                    <span>الرسائل</span>
                </a>
            </li>
            
            <li class="<?php echo $current_page === 'trending' ? 'active' : ''; ?>">
                <a href="trending.php">
                    <i class="fas fa-fire"></i>
                    <span>المحتوى الشائع</span>
                </a>
            </li>
            
            <li class="<?php echo $current_page === 'settings' ? 'active' : ''; ?>">
                <a href="settings.php">
                    <i class="fas fa-cog"></i>
                    <span>إعدادات النظام</span>
                </a>
            </li>
            
            <li class="<?php echo $current_page === 'logs' ? 'active' : ''; ?>">
                <a href="logs.php">
                    <i class="fas fa-history"></i>
                    <span>سجل الأنشطة</span>
                </a>
            </li>
            
            <?php if ($user['role'] === 'admin'): ?>
            <li class="<?php echo $current_page === 'backup' ? 'active' : ''; ?>">
                <a href="backup.php">
                    <i class="fas fa-database"></i>
                    <span>النسخ الاحتياطي</span>
                </a>
            </li>
            <?php endif; ?>
        </ul>
    </nav>
    
    <div class="sidebar-footer">
        <a href="../index.php" class="back-to-site">
            <i class="fas fa-arrow-left"></i>
            <span>العودة للموقع</span>
        </a>
        
        <a href="../auth/logout.php" class="logout-btn">
            <i class="fas fa-sign-out-alt"></i>
            <span>تسجيل الخروج</span>
        </a>
    </div>
</aside>

<style>
.logout-btn {
    display: flex;
    align-items: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    text-decoration: none;
    border-radius: 10px;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.logout-btn i {
    margin-left: 10px;
}
</style>
