// JavaScript للوحة تحكم الإدارة - HubHam

// متغيرات عامة
let userGrowthChart, postActivityChart;

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    setupEventListeners();
    loadDashboardData();
});

// تهيئة الرسوم البيانية
function initializeCharts() {
    // رسم نمو المستخدمين
    const userGrowthCtx = document.getElementById('userGrowthChart');
    if (userGrowthCtx) {
        userGrowthChart = new Chart(userGrowthCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'مستخدمون جدد',
                    data: [],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    }
                }
            }
        });
    }

    // رسم نشاط المنشورات
    const postActivityCtx = document.getElementById('postActivityChart');
    if (postActivityCtx) {
        postActivityChart = new Chart(postActivityCtx, {
            type: 'bar',
            data: {
                labels: [],
                datasets: [{
                    label: 'منشورات جديدة',
                    data: [],
                    backgroundColor: 'rgba(245, 87, 108, 0.8)',
                    borderColor: '#f5576c',
                    borderWidth: 2,
                    borderRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    }
                }
            }
        });
    }
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // تبديل الشريط الجانبي
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', toggleSidebar);
    }

    // تغيير فترة الرسم البياني
    const userGrowthPeriod = document.getElementById('userGrowthPeriod');
    if (userGrowthPeriod) {
        userGrowthPeriod.addEventListener('change', function() {
            loadUserGrowthData(this.value);
        });
    }

    const postActivityPeriod = document.getElementById('postActivityPeriod');
    if (postActivityPeriod) {
        postActivityPeriod.addEventListener('change', function() {
            loadPostActivityData(this.value);
        });
    }

    // إغلاق الشريط الجانبي عند النقر خارجه
    document.addEventListener('click', function(e) {
        const sidebar = document.querySelector('.admin-sidebar');
        const toggle = document.querySelector('.sidebar-toggle');
        
        if (window.innerWidth <= 1024 && 
            !sidebar.contains(e.target) && 
            !toggle.contains(e.target) && 
            sidebar.classList.contains('open')) {
            sidebar.classList.remove('open');
        }
    });
}

// تبديل الشريط الجانبي
function toggleSidebar() {
    const sidebar = document.querySelector('.admin-sidebar');
    sidebar.classList.toggle('open');
}

// تحميل بيانات لوحة التحكم
function loadDashboardData() {
    loadUserGrowthData(30);
    loadPostActivityData(30);
}

// تحميل بيانات نمو المستخدمين
async function loadUserGrowthData(days) {
    try {
        const response = await fetch(`../api/admin/user-growth.php?days=${days}`);
        const data = await response.json();
        
        if (data.success && userGrowthChart) {
            userGrowthChart.data.labels = data.labels;
            userGrowthChart.data.datasets[0].data = data.data;
            userGrowthChart.update();
        }
    } catch (error) {
        console.error('خطأ في تحميل بيانات نمو المستخدمين:', error);
    }
}

// تحميل بيانات نشاط المنشورات
async function loadPostActivityData(days) {
    try {
        const response = await fetch(`../api/admin/post-activity.php?days=${days}`);
        const data = await response.json();
        
        if (data.success && postActivityChart) {
            postActivityChart.data.labels = data.labels;
            postActivityChart.data.datasets[0].data = data.data;
            postActivityChart.update();
        }
    } catch (error) {
        console.error('خطأ في تحميل بيانات نشاط المنشورات:', error);
    }
}

// عرض تفاصيل المستخدم
function viewUser(userId) {
    window.open(`user-details.php?id=${userId}`, '_blank', 'width=800,height=600');
}

// تعديل المستخدم
function editUser(userId) {
    window.location.href = `edit-user.php?id=${userId}`;
}

// عرض تفاصيل التقرير
function viewReport(reportId) {
    window.open(`report-details.php?id=${reportId}`, '_blank', 'width=800,height=600');
}

// الموافقة على التقرير
async function approveReport(reportId) {
    if (!confirm('هل أنت متأكد من الموافقة على هذا التقرير؟')) {
        return;
    }

    try {
        const response = await fetch('../api/admin/approve-report.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ report_id: reportId })
        });

        const data = await response.json();
        
        if (data.success) {
            showNotification('تم الموافقة على التقرير بنجاح', 'success');
            location.reload();
        } else {
            showNotification(data.message || 'حدث خطأ', 'error');
        }
    } catch (error) {
        console.error('خطأ في الموافقة على التقرير:', error);
        showNotification('حدث خطأ في الخادم', 'error');
    }
}

// رفض التقرير
async function rejectReport(reportId) {
    if (!confirm('هل أنت متأكد من رفض هذا التقرير؟')) {
        return;
    }

    try {
        const response = await fetch('../api/admin/reject-report.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ report_id: reportId })
        });

        const data = await response.json();
        
        if (data.success) {
            showNotification('تم رفض التقرير', 'info');
            location.reload();
        } else {
            showNotification(data.message || 'حدث خطأ', 'error');
        }
    } catch (error) {
        console.error('خطأ في رفض التقرير:', error);
        showNotification('حدث خطأ في الخادم', 'error');
    }
}

// عرض الإشعارات
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    // إضافة الأنماط
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        left: 20px;
        background: ${getNotificationColor(type)};
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: space-between;
        min-width: 300px;
        animation: slideIn 0.3s ease;
    `;

    // إضافة إلى الصفحة
    document.body.appendChild(notification);

    // إزالة تلقائية بعد 5 ثوان
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }
    }, 5000);
}

// الحصول على أيقونة الإشعار
function getNotificationIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// الحصول على لون الإشعار
function getNotificationColor(type) {
    const colors = {
        success: '#28a745',
        error: '#dc3545',
        warning: '#ffc107',
        info: '#17a2b8'
    };
    return colors[type] || '#17a2b8';
}

// إضافة أنماط الحركة
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(-100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(-100%);
            opacity: 0;
        }
    }
    
    .notification-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .notification-close {
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        padding: 5px;
        border-radius: 3px;
        transition: background 0.3s ease;
    }
    
    .notification-close:hover {
        background: rgba(255, 255, 255, 0.2);
    }
`;
document.head.appendChild(style);

// تحديث الوقت الحقيقي
setInterval(() => {
    const timeElements = document.querySelectorAll('.real-time');
    timeElements.forEach(element => {
        const timestamp = element.dataset.timestamp;
        if (timestamp) {
            element.textContent = formatTimeAgo(timestamp);
        }
    });
}, 60000); // تحديث كل دقيقة

// تنسيق الوقت المنقضي
function formatTimeAgo(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diff = Math.floor((now - time) / 1000);

    if (diff < 60) return 'الآن';
    if (diff < 3600) return `${Math.floor(diff / 60)} دقيقة`;
    if (diff < 86400) return `${Math.floor(diff / 3600)} ساعة`;
    if (diff < 2592000) return `${Math.floor(diff / 86400)} يوم`;
    if (diff < 31536000) return `${Math.floor(diff / 2592000)} شهر`;
    return `${Math.floor(diff / 31536000)} سنة`;
}
