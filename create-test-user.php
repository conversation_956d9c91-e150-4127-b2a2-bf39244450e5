<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>إنشاء مستخدمين تجريبيين</h1>";

try {
    require_once 'config/database.php';
    
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات</p>";
    
    // إنشاء مستخدم إداري
    $admin_email = '<EMAIL>';
    $admin_password = password_hash('HubHam@2024', PASSWORD_DEFAULT);
    
    // التحقق من وجود المستخدم الإداري
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
    $stmt->execute([$admin_email]);
    
    if ($stmt->fetchColumn() == 0) {
        $stmt = $pdo->prepare("INSERT INTO users (username, email, password, display_name, role, status, email_verified) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $result = $stmt->execute([
            'hubham_admin',
            $admin_email,
            $admin_password,
            'مدير HubHam',
            'admin',
            'active',
            1
        ]);
        
        if ($result) {
            echo "<p style='color: green;'>✅ تم إنشاء المستخدم الإداري</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ المستخدم الإداري موجود مسبقاً</p>";
    }
    
    // إنشاء مستخدم تجريبي
    $test_email = '<EMAIL>';
    $test_password = password_hash('Test@123', PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
    $stmt->execute([$test_email]);
    
    if ($stmt->fetchColumn() == 0) {
        $stmt = $pdo->prepare("INSERT INTO users (username, email, password, display_name, role, status, email_verified) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $result = $stmt->execute([
            'test_user',
            $test_email,
            $test_password,
            'مستخدم تجريبي',
            'user',
            'active',
            1
        ]);
        
        if ($result) {
            echo "<p style='color: green;'>✅ تم إنشاء المستخدم التجريبي</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ المستخدم التجريبي موجود مسبقاً</p>";
    }
    
    // عرض جميع المستخدمين
    echo "<h2>المستخدمون الموجودون:</h2>";
    $stmt = $pdo->query("SELECT id, username, email, role, status FROM users ORDER BY id");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>اسم المستخدم</th><th>البريد الإلكتروني</th><th>الدور</th><th>الحالة</th></tr>";
    
    foreach ($users as $user) {
        echo "<tr>";
        echo "<td>" . $user['id'] . "</td>";
        echo "<td>" . htmlspecialchars($user['username']) . "</td>";
        echo "<td>" . htmlspecialchars($user['email']) . "</td>";
        echo "<td>" . htmlspecialchars($user['role']) . "</td>";
        echo "<td>" . htmlspecialchars($user['status']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>بيانات الدخول:</h2>";
    echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 10px; margin: 10px 0;'>";
    echo "<h3>المدير:</h3>";
    echo "<strong>البريد الإلكتروني:</strong> <EMAIL><br>";
    echo "<strong>كلمة المرور:</strong> HubHam@2024<br>";
    echo "</div>";
    
    echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 10px; margin: 10px 0;'>";
    echo "<h3>المستخدم التجريبي:</h3>";
    echo "<strong>البريد الإلكتروني:</strong> <EMAIL><br>";
    echo "<strong>كلمة المرور:</strong> Test@123<br>";
    echo "</div>";
    
    echo "<p><a href='simple-login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تجربة تسجيل الدخول</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>
