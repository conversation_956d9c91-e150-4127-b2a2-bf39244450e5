<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: simple-login.php');
    exit;
}

try {
    require_once 'config/database.php';
    
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    // إحصائيات
    $stats = [];
    
    // عدد المستخدمين
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $stats['users'] = $stmt->fetchColumn();
    
    // عدد المنشورات (إذا كان الجدول موجود)
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM posts");
        $stats['posts'] = $stmt->fetchColumn();
    } catch (Exception $e) {
        $stats['posts'] = 0;
    }
    
    // عدد التعليقات (إذا كان الجدول موجود)
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM comments");
        $stats['comments'] = $stmt->fetchColumn();
    } catch (Exception $e) {
        $stats['comments'] = 0;
    }
    
    // أحدث المستخدمين
    $stmt = $pdo->query("SELECT username, email, created_at FROM users ORDER BY created_at DESC LIMIT 5");
    $recent_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - HubHam</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .welcome {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        
        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #666;
            font-size: 18px;
        }
        
        .recent-section {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }
        
        .recent-section h3 {
            margin-bottom: 20px;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .user-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }
        
        .user-item:last-child {
            border-bottom: none;
        }
        
        .user-details {
            flex: 1;
        }
        
        .user-name {
            font-weight: bold;
            color: #333;
        }
        
        .user-email {
            color: #666;
            font-size: 14px;
        }
        
        .user-date {
            color: #999;
            font-size: 12px;
        }
        
        .btn {
            display: inline-block;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            margin: 5px;
            transition: transform 0.3s ease;
            font-weight: bold;
            border: none;
            cursor: pointer;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }
        
        .action-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">💎 HubHam</div>
            <div class="user-info">
                <span>مرحباً، <?php echo htmlspecialchars($_SESSION['username']); ?></span>
                <span class="badge"><?php echo htmlspecialchars($_SESSION['role'] ?? 'user'); ?></span>
                <a href="simple-login.php?logout=1" class="btn btn-danger">خروج</a>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="welcome">
            <h1>لوحة تحكم HubHam</h1>
            <p>مرحباً بك في لوحة التحكم الخاصة بمنصة HubHam</p>
        </div>
        
        <?php if (isset($error)): ?>
            <div class="error">
                خطأ في قاعدة البيانات: <?php echo htmlspecialchars($error); ?>
            </div>
        <?php else: ?>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-number"><?php echo number_format($stats['users']); ?></div>
                    <div class="stat-label">المستخدمون</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">📝</div>
                    <div class="stat-number"><?php echo number_format($stats['posts']); ?></div>
                    <div class="stat-label">المنشورات</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">💬</div>
                    <div class="stat-number"><?php echo number_format($stats['comments']); ?></div>
                    <div class="stat-label">التعليقات</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">🔥</div>
                    <div class="stat-number"><?php echo date('H:i'); ?></div>
                    <div class="stat-label">الوقت الحالي</div>
                </div>
            </div>
            
            <div class="recent-section">
                <h3>أحدث المستخدمين</h3>
                <?php if (!empty($recent_users)): ?>
                    <?php foreach ($recent_users as $user): ?>
                        <div class="user-item">
                            <div class="user-details">
                                <div class="user-name"><?php echo htmlspecialchars($user['username']); ?></div>
                                <div class="user-email"><?php echo htmlspecialchars($user['email']); ?></div>
                            </div>
                            <div class="user-date">
                                <?php echo date('Y-m-d H:i', strtotime($user['created_at'])); ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p>لا توجد بيانات مستخدمين</p>
                <?php endif; ?>
            </div>
            
        <?php endif; ?>
        
        <div class="actions">
            <div class="action-card">
                <h4>إدارة المستخدمين</h4>
                <a href="create-test-user.php" class="btn">عرض المستخدمين</a>
            </div>
            
            <div class="action-card">
                <h4>إعدادات النظام</h4>
                <a href="debug.php" class="btn">تشخيص النظام</a>
            </div>
            
            <div class="action-card">
                <h4>قاعدة البيانات</h4>
                <a href="setup-hubham.php" class="btn">إعداد الجداول</a>
            </div>
            
            <div class="action-card">
                <h4>الاختبارات</h4>
                <a href="test-all.php" class="btn">اختبار شامل</a>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 40px;">
            <a href="simple-index.php" class="btn btn-secondary">العودة للرئيسية</a>
        </div>
    </div>
</body>
</html>

<?php
// معالجة تسجيل الخروج
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: simple-login.php');
    exit;
}
?>
