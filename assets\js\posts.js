// متغيرات خاصة بالمنشورات
let selectedMedia = null;
let isPublishing = false;

// نشر منشور جديد
async function publishPost() {
    if (isPublishing) return;
    
    const postContent = document.getElementById('postContent');
    const publishBtn = document.getElementById('publishBtn');
    
    if (!postContent || !postContent.value.trim()) {
        showNotification('يرجى كتابة محتوى المنشور', 'error');
        return;
    }
    
    if (postContent.value.length > 280) {
        showNotification('المنشور طويل جداً', 'error');
        return;
    }
    
    isPublishing = true;
    publishBtn.disabled = true;
    publishBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري النشر...';
    
    try {
        const formData = new FormData();
        formData.append('content', postContent.value.trim());
        
        if (selectedMedia) {
            formData.append('media', selectedMedia.file);
            formData.append('media_type', selectedMedia.type);
        }
        
        // إضافة الموقع إذا كان متاحاً
        if (navigator.geolocation) {
            try {
                const position = await getCurrentPosition();
                formData.append('latitude', position.coords.latitude);
                formData.append('longitude', position.coords.longitude);
            } catch (error) {
                console.log('Location not available');
            }
        }
        
        const response = await fetch('api/posts/create.php', {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            showNotification('تم نشر المنشور بنجاح', 'success');
            
            // إعادة تعيين النموذج
            postContent.value = '';
            selectedMedia = null;
            updateCharCount();
            clearMediaPreview();
            
            // تحديث الخلاصة
            HubHam.refreshFeed();
            
            // تحديث الإحصائيات
            loadUserStats();
        } else {
            showNotification(data.message || 'حدث خطأ في نشر المنشور', 'error');
        }
    } catch (error) {
        console.error('Error publishing post:', error);
        showNotification('حدث خطأ في الاتصال', 'error');
    } finally {
        isPublishing = false;
        publishBtn.disabled = false;
        publishBtn.innerHTML = 'نشر';
    }
}

// الحصول على الموقع الحالي
function getCurrentPosition() {
    return new Promise((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, {
            enableHighAccuracy: true,
            timeout: 5000,
            maximumAge: 300000
        });
    });
}

// تبديل الإعجاب
async function toggleLike(postId) {
    const likeBtn = document.querySelector(`[data-post-id="${postId}"] .like-btn`);
    if (!likeBtn) return;
    
    const isLiked = likeBtn.classList.contains('liked');
    const countSpan = likeBtn.querySelector('span');
    let currentCount = parseInt(countSpan.textContent) || 0;
    
    // تحديث الواجهة فوراً
    if (isLiked) {
        likeBtn.classList.remove('liked');
        countSpan.textContent = Math.max(0, currentCount - 1);
    } else {
        likeBtn.classList.add('liked');
        countSpan.textContent = currentCount + 1;
        
        // تأثير الإعجاب
        createLikeAnimation(likeBtn);
    }
    
    try {
        const response = await fetch('api/posts/like.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ post_id: postId })
        });
        
        const data = await response.json();
        
        if (!data.success) {
            // إعادة الحالة السابقة في حالة الخطأ
            if (isLiked) {
                likeBtn.classList.add('liked');
                countSpan.textContent = currentCount;
            } else {
                likeBtn.classList.remove('liked');
                countSpan.textContent = Math.max(0, currentCount);
            }
            showNotification(data.message || 'حدث خطأ', 'error');
        } else {
            // تحديث العدد الصحيح من الخادم
            countSpan.textContent = data.likes_count;
        }
    } catch (error) {
        console.error('Error toggling like:', error);
        // إعادة الحالة السابقة
        if (isLiked) {
            likeBtn.classList.add('liked');
            countSpan.textContent = currentCount;
        } else {
            likeBtn.classList.remove('liked');
            countSpan.textContent = Math.max(0, currentCount);
        }
        showNotification('حدث خطأ في الاتصال', 'error');
    }
}

// إنشاء تأثير الإعجاب
function createLikeAnimation(button) {
    const heart = document.createElement('i');
    heart.className = 'fas fa-heart like-animation';
    heart.style.position = 'absolute';
    heart.style.color = '#e74c3c';
    heart.style.fontSize = '20px';
    heart.style.pointerEvents = 'none';
    heart.style.zIndex = '1000';
    
    const rect = button.getBoundingClientRect();
    heart.style.left = rect.left + rect.width / 2 + 'px';
    heart.style.top = rect.top + rect.height / 2 + 'px';
    
    document.body.appendChild(heart);
    
    // تحريك القلب
    heart.animate([
        { transform: 'translate(-50%, -50%) scale(1)', opacity: 1 },
        { transform: 'translate(-50%, -100px) scale(1.5)', opacity: 0 }
    ], {
        duration: 1000,
        easing: 'ease-out'
    }).onfinish = () => {
        heart.remove();
    };
}

// عرض التعليقات
async function showComments(postId) {
    try {
        const response = await fetch(`api/posts/comments.php?post_id=${postId}`);
        const data = await response.json();
        
        if (data.success) {
            openCommentsModal(postId, data.comments);
        } else {
            showNotification(data.message || 'حدث خطأ في تحميل التعليقات', 'error');
        }
    } catch (error) {
        console.error('Error loading comments:', error);
        showNotification('حدث خطأ في الاتصال', 'error');
    }
}

// فتح نافذة التعليقات
function openCommentsModal(postId, comments) {
    const modal = document.createElement('div');
    modal.className = 'modal show';
    modal.id = 'commentsModal';
    
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>التعليقات</h3>
                <button class="close-btn" onclick="closeModal('commentsModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="comment-form">
                    <textarea id="commentContent" placeholder="اكتب تعليقاً..." maxlength="280"></textarea>
                    <button onclick="addComment(${postId})" class="post-btn">تعليق</button>
                </div>
                <div class="comments-list" id="commentsList">
                    ${comments.map(comment => createCommentElement(comment)).join('')}
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // التركيز على حقل التعليق
    setTimeout(() => {
        const commentInput = document.getElementById('commentContent');
        if (commentInput) commentInput.focus();
    }, 100);
}

// إنشاء عنصر تعليق
function createCommentElement(comment) {
    const timeAgo = HubHam.formatTimeAgo(comment.created_at);
    
    return `
        <div class="comment-item" data-comment-id="${comment.id}">
            <div class="comment-header">
                <img src="${comment.profile_image || 'assets/images/default-avatar.png'}" alt="صورة المستخدم" class="comment-avatar">
                <div class="comment-user-info">
                    <div class="comment-user-name">
                        ${comment.display_name}
                        ${comment.role === 'admin' ? '<i class="fas fa-crown admin-badge" title="مدير"></i>' : ''}
                    </div>
                    <div class="comment-username">@${comment.username}</div>
                </div>
                <div class="comment-time">${timeAgo}</div>
            </div>
            <div class="comment-content">${formatPostContent(comment.content)}</div>
            <div class="comment-actions">
                <button class="action-btn like-btn" onclick="likeComment(${comment.id})">
                    <i class="fas fa-heart"></i>
                    <span>${comment.likes_count || 0}</span>
                </button>
                <button class="action-btn reply-btn" onclick="replyToComment(${comment.id})">
                    <i class="fas fa-reply"></i>
                    <span>رد</span>
                </button>
            </div>
        </div>
    `;
}

// إضافة تعليق
async function addComment(postId) {
    const commentContent = document.getElementById('commentContent');
    if (!commentContent || !commentContent.value.trim()) {
        showNotification('يرجى كتابة تعليق', 'error');
        return;
    }
    
    try {
        const response = await fetch('api/posts/comment.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                post_id: postId,
                content: commentContent.value.trim()
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showNotification('تم إضافة التعليق بنجاح', 'success');
            
            // إضافة التعليق الجديد إلى القائمة
            const commentsList = document.getElementById('commentsList');
            if (commentsList) {
                const newComment = createCommentElement(data.comment);
                commentsList.insertAdjacentHTML('afterbegin', newComment);
            }
            
            // مسح حقل التعليق
            commentContent.value = '';
            
            // تحديث عداد التعليقات في المنشور
            const postElement = document.querySelector(`[data-post-id="${postId}"]`);
            if (postElement) {
                const commentBtn = postElement.querySelector('.comment-btn span');
                if (commentBtn) {
                    const currentCount = parseInt(commentBtn.textContent) || 0;
                    commentBtn.textContent = currentCount + 1;
                }
            }
        } else {
            showNotification(data.message || 'حدث خطأ في إضافة التعليق', 'error');
        }
    } catch (error) {
        console.error('Error adding comment:', error);
        showNotification('حدث خطأ في الاتصال', 'error');
    }
}

// مشاركة منشور
async function sharePost(postId) {
    if (navigator.share) {
        try {
            await navigator.share({
                title: 'منشور من HubHam',
                text: 'شاهد هذا المنشور على HubHam',
                url: `${window.location.origin}/post.php?id=${postId}`
            });
        } catch (error) {
            console.log('Error sharing:', error);
            copyPostLink(postId);
        }
    } else {
        copyPostLink(postId);
    }
}

// نسخ رابط المنشور
function copyPostLink(postId) {
    const url = `${window.location.origin}/post.php?id=${postId}`;
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
            showNotification('تم نسخ الرابط', 'success');
        }).catch(() => {
            fallbackCopyTextToClipboard(url);
        });
    } else {
        fallbackCopyTextToClipboard(url);
    }
}

// نسخ النص (طريقة بديلة)
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        document.execCommand('copy');
        showNotification('تم نسخ الرابط', 'success');
    } catch (err) {
        showNotification('فشل في نسخ الرابط', 'error');
    }
    
    document.body.removeChild(textArea);
}

// تبديل الحفظ
async function toggleBookmark(postId) {
    const bookmarkBtn = document.querySelector(`[data-post-id="${postId}"] .bookmark-btn`);
    if (!bookmarkBtn) return;
    
    const isBookmarked = bookmarkBtn.classList.contains('bookmarked');
    
    try {
        const response = await fetch('api/posts/bookmark.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ post_id: postId })
        });
        
        const data = await response.json();
        
        if (data.success) {
            if (data.action === 'bookmarked') {
                bookmarkBtn.classList.add('bookmarked');
                bookmarkBtn.querySelector('i').style.color = '#667eea';
                showNotification('تم حفظ المنشور', 'success');
            } else {
                bookmarkBtn.classList.remove('bookmarked');
                bookmarkBtn.querySelector('i').style.color = '';
                showNotification('تم إلغاء حفظ المنشور', 'success');
            }
        } else {
            showNotification(data.message || 'حدث خطأ', 'error');
        }
    } catch (error) {
        console.error('Error toggling bookmark:', error);
        showNotification('حدث خطأ في الاتصال', 'error');
    }
}

// إرفاق صورة
function attachImage() {
    const imageInput = document.getElementById('imageInput');
    if (imageInput) {
        imageInput.click();
    }
}

// إرفاق فيديو
function attachVideo() {
    const videoInput = document.getElementById('videoInput');
    if (videoInput) {
        videoInput.click();
    }
}

// معالجة رفع الصورة
function handleImageUpload(input) {
    const file = input.files[0];
    if (!file) return;
    
    // التحقق من نوع الملف
    if (!file.type.startsWith('image/')) {
        showNotification('يرجى اختيار ملف صورة صحيح', 'error');
        return;
    }
    
    // التحقق من حجم الملف (5 ميجابايت)
    if (file.size > 5 * 1024 * 1024) {
        showNotification('حجم الصورة كبير جداً (الحد الأقصى 5 ميجابايت)', 'error');
        return;
    }
    
    selectedMedia = {
        file: file,
        type: 'image'
    };
    
    // عرض معاينة الصورة
    const reader = new FileReader();
    reader.onload = function(e) {
        showMediaPreview(e.target.result, 'image');
    };
    reader.readAsDataURL(file);
}

// معالجة رفع الفيديو
function handleVideoUpload(input) {
    const file = input.files[0];
    if (!file) return;
    
    // التحقق من نوع الملف
    if (!file.type.startsWith('video/')) {
        showNotification('يرجى اختيار ملف فيديو صحيح', 'error');
        return;
    }
    
    // التحقق من حجم الملف (50 ميجابايت)
    if (file.size > 50 * 1024 * 1024) {
        showNotification('حجم الفيديو كبير جداً (الحد الأقصى 50 ميجابايت)', 'error');
        return;
    }
    
    selectedMedia = {
        file: file,
        type: 'video'
    };
    
    // عرض معاينة الفيديو
    const reader = new FileReader();
    reader.onload = function(e) {
        showMediaPreview(e.target.result, 'video');
    };
    reader.readAsDataURL(file);
}

// عرض معاينة الوسائط
function showMediaPreview(src, type) {
    // إزالة المعاينة السابقة
    clearMediaPreview();
    
    const composerTools = document.querySelector('.composer-tools');
    const previewDiv = document.createElement('div');
    previewDiv.className = 'media-preview';
    previewDiv.innerHTML = `
        <div class="preview-container">
            ${type === 'image' ? 
                `<img src="${src}" alt="معاينة الصورة">` : 
                `<video src="${src}" controls></video>`
            }
            <button class="remove-media" onclick="clearMediaPreview()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    composerTools.parentNode.insertBefore(previewDiv, composerTools);
}

// مسح معاينة الوسائط
function clearMediaPreview() {
    const preview = document.querySelector('.media-preview');
    if (preview) {
        preview.remove();
    }
    selectedMedia = null;
    
    // مسح قيم الإدخال
    const imageInput = document.getElementById('imageInput');
    const videoInput = document.getElementById('videoInput');
    if (imageInput) imageInput.value = '';
    if (videoInput) videoInput.value = '';
}

// إضافة رمز
function addSymbol() {
    const modal = document.getElementById('symbolModal');
    if (modal) {
        modal.classList.add('show');
    }
}

// إدراج رمز
function insertSymbol(symbol) {
    const postContent = document.getElementById('postContent');
    if (postContent) {
        const cursorPos = postContent.selectionStart;
        const textBefore = postContent.value.substring(0, cursorPos);
        const textAfter = postContent.value.substring(postContent.selectionEnd);
        
        postContent.value = textBefore + symbol + textAfter;
        postContent.selectionStart = postContent.selectionEnd = cursorPos + symbol.length;
        postContent.focus();
        
        updateCharCount();
    }
    
    closeModal('symbolModal');
}

// إضافة موقع
function addLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function(position) {
                showNotification('تم إضافة الموقع إلى المنشور', 'success');
                // يمكن إضافة المزيد من المعالجة هنا
            },
            function(error) {
                showNotification('لا يمكن الوصول إلى الموقع', 'error');
            }
        );
    } else {
        showNotification('المتصفح لا يدعم خدمة الموقع', 'error');
    }
}

// تحديث عداد الأحرف
function updateCharCount() {
    const postContent = document.getElementById('postContent');
    const charCount = document.getElementById('charCount');
    const publishBtn = document.getElementById('publishBtn');
    
    if (postContent && charCount) {
        const remaining = 280 - postContent.value.length;
        charCount.textContent = remaining;
        charCount.style.color = remaining < 20 ? '#e74c3c' : '#657786';
        
        if (publishBtn) {
            publishBtn.disabled = remaining < 0 || postContent.value.trim().length === 0;
        }
    }
}

// تهيئة مستمعي الأحداث للمنشورات
document.addEventListener('DOMContentLoaded', function() {
    const postContent = document.getElementById('postContent');
    if (postContent) {
        postContent.addEventListener('input', updateCharCount);
    }
});
