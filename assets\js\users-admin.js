// JavaScript لإدارة المستخدمين - <PERSON><PERSON><PERSON><PERSON> Admin

// عرض تفاصيل المستخدم
async function viewUser(userId) {
    try {
        const response = await fetch(`../api/admin/user-details.php?id=${userId}`);
        const data = await response.json();
        
        if (data.success) {
            document.getElementById('userModalBody').innerHTML = generateUserDetailsHTML(data.user);
            openModal('userModal');
        } else {
            showNotification(data.message || 'حدث خطأ', 'error');
        }
    } catch (error) {
        console.error('خطأ في جلب تفاصيل المستخدم:', error);
        showNotification('حدث خطأ في الخادم', 'error');
    }
}

// إيقاف المستخدم
async function suspendUser(userId) {
    if (!confirm('هل أنت متأكد من إيقاف هذا المستخدم؟')) {
        return;
    }

    try {
        const response = await fetch('../api/admin/suspend-user.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ user_id: userId })
        });

        const data = await response.json();
        
        if (data.success) {
            showNotification('تم إيقاف المستخدم بنجاح', 'success');
            location.reload();
        } else {
            showNotification(data.message || 'حدث خطأ', 'error');
        }
    } catch (error) {
        console.error('خطأ في إيقاف المستخدم:', error);
        showNotification('حدث خطأ في الخادم', 'error');
    }
}

// تفعيل المستخدم
async function activateUser(userId) {
    if (!confirm('هل أنت متأكد من تفعيل هذا المستخدم؟')) {
        return;
    }

    try {
        const response = await fetch('../api/admin/activate-user.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ user_id: userId })
        });

        const data = await response.json();
        
        if (data.success) {
            showNotification('تم تفعيل المستخدم بنجاح', 'success');
            location.reload();
        } else {
            showNotification(data.message || 'حدث خطأ', 'error');
        }
    } catch (error) {
        console.error('خطأ في تفعيل المستخدم:', error);
        showNotification('حدث خطأ في الخادم', 'error');
    }
}

// حظر المستخدم
async function banUser(userId) {
    if (!confirm('هل أنت متأكد من حظر هذا المستخدم؟ هذا الإجراء خطير!')) {
        return;
    }

    try {
        const response = await fetch('../api/admin/ban-user.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ user_id: userId })
        });

        const data = await response.json();
        
        if (data.success) {
            showNotification('تم حظر المستخدم بنجاح', 'warning');
            location.reload();
        } else {
            showNotification(data.message || 'حدث خطأ', 'error');
        }
    } catch (error) {
        console.error('خطأ في حظر المستخدم:', error);
        showNotification('حدث خطأ في الخادم', 'error');
    }
}

// ترقية المستخدم
function promoteUser(userId) {
    document.getElementById('promoteUserId').value = userId;
    openModal('promoteModal');
}

// تصدير المستخدمين
async function exportUsers() {
    try {
        const params = new URLSearchParams(window.location.search);
        const response = await fetch(`../api/admin/export-users.php?${params.toString()}`);
        
        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `users_export_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            showNotification('تم تصدير البيانات بنجاح', 'success');
        } else {
            showNotification('فشل في تصدير البيانات', 'error');
        }
    } catch (error) {
        console.error('خطأ في تصدير البيانات:', error);
        showNotification('حدث خطأ في الخادم', 'error');
    }
}

// فتح النافذة المنبثقة
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
    
    // إضافة مستمع للإغلاق بالنقر خارج النافذة
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal(modalId);
        }
    });
}

// إغلاق النافذة المنبثقة
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

// إنشاء HTML لتفاصيل المستخدم
function generateUserDetailsHTML(user) {
    return `
        <div class="user-details">
            <div class="user-header">
                <img src="${user.profile_image || '../assets/images/default-avatar.png'}" alt="صورة المستخدم" class="user-avatar">
                <div class="user-basic-info">
                    <h3>${user.display_name}</h3>
                    <p>@${user.username}</p>
                    <span class="status-badge ${user.status}">${getStatusText(user.status)}</span>
                    <span class="role-badge ${user.role}">${getRoleText(user.role)}</span>
                </div>
            </div>
            
            <div class="user-info-grid">
                <div class="info-item">
                    <label>البريد الإلكتروني:</label>
                    <span>${user.email}</span>
                </div>
                
                <div class="info-item">
                    <label>رقم الهاتف:</label>
                    <span>${user.phone || 'غير محدد'}</span>
                </div>
                
                <div class="info-item">
                    <label>الموقع:</label>
                    <span>${user.location || 'غير محدد'}</span>
                </div>
                
                <div class="info-item">
                    <label>تاريخ الميلاد:</label>
                    <span>${user.birth_date || 'غير محدد'}</span>
                </div>
                
                <div class="info-item">
                    <label>تاريخ التسجيل:</label>
                    <span>${formatDate(user.created_at)}</span>
                </div>
                
                <div class="info-item">
                    <label>آخر نشاط:</label>
                    <span>${user.last_activity ? formatDate(user.last_activity) : 'لم يسجل دخول'}</span>
                </div>
                
                <div class="info-item">
                    <label>عدد المتابعين:</label>
                    <span>${user.followers_count || 0}</span>
                </div>
                
                <div class="info-item">
                    <label>عدد المتابعين:</label>
                    <span>${user.following_count || 0}</span>
                </div>
                
                <div class="info-item">
                    <label>عدد المنشورات:</label>
                    <span>${user.posts_count || 0}</span>
                </div>
                
                <div class="info-item">
                    <label>تغييرات البريد الإلكتروني:</label>
                    <span>${user.email_changes || 0}/3</span>
                </div>
            </div>
            
            ${user.bio ? `
                <div class="user-bio">
                    <label>النبذة الشخصية:</label>
                    <p>${user.bio}</p>
                </div>
            ` : ''}
            
            <div class="user-stats">
                <h4>إحصائيات النشاط</h4>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-number">${user.total_likes || 0}</span>
                        <span class="stat-label">إعجابات</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">${user.total_comments || 0}</span>
                        <span class="stat-label">تعليقات</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">${user.total_shares || 0}</span>
                        <span class="stat-label">مشاركات</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">${user.reports_count || 0}</span>
                        <span class="stat-label">تقارير</span>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// تنسيق التاريخ
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// الحصول على نص الحالة
function getStatusText(status) {
    const statuses = {
        'active': 'نشط',
        'pending': 'معلق',
        'suspended': 'موقوف',
        'banned': 'محظور'
    };
    return statuses[status] || status;
}

// الحصول على نص الدور
function getRoleText(role) {
    const roles = {
        'user': 'مستخدم',
        'moderator': 'مراقب',
        'admin': 'مدير'
    };
    return roles[role] || role;
}

// إضافة أنماط CSS للنوافذ المنبثقة
const modalStyles = `
    .modal {
        display: none;
        position: fixed;
        z-index: 10000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        align-items: center;
        justify-content: center;
    }
    
    .modal-content {
        background: white;
        border-radius: 15px;
        max-width: 800px;
        width: 90%;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    }
    
    .modal-header {
        padding: 25px 30px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .modal-header h3 {
        margin: 0;
        color: #333;
        font-size: 24px;
    }
    
    .modal-close {
        background: none;
        border: none;
        font-size: 24px;
        color: #666;
        cursor: pointer;
        padding: 5px;
        border-radius: 5px;
        transition: all 0.3s ease;
    }
    
    .modal-close:hover {
        background: #f0f0f0;
        color: #333;
    }
    
    .modal-body {
        padding: 30px;
    }
    
    .user-details .user-header {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
    }
    
    .user-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid #667eea;
    }
    
    .user-basic-info h3 {
        margin: 0 0 5px 0;
        color: #333;
        font-size: 24px;
    }
    
    .user-basic-info p {
        margin: 0 0 10px 0;
        color: #666;
        font-size: 16px;
    }
    
    .user-info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .info-item {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }
    
    .info-item label {
        font-weight: 600;
        color: #333;
        font-size: 14px;
    }
    
    .info-item span {
        color: #666;
        font-size: 16px;
    }
    
    .user-bio {
        margin-bottom: 30px;
    }
    
    .user-bio label {
        font-weight: 600;
        color: #333;
        display: block;
        margin-bottom: 10px;
    }
    
    .user-bio p {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 10px;
        margin: 0;
        line-height: 1.6;
    }
    
    .user-stats h4 {
        margin: 0 0 20px 0;
        color: #333;
        font-size: 20px;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 20px;
    }
    
    .stat-item {
        text-align: center;
        background: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
    }
    
    .stat-number {
        display: block;
        font-size: 24px;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 5px;
    }
    
    .stat-label {
        font-size: 14px;
        color: #666;
    }
    
    .role-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        margin-left: 10px;
    }
    
    .role-badge.user {
        background: #e3f2fd;
        color: #1976d2;
    }
    
    .role-badge.moderator {
        background: #fff3e0;
        color: #f57c00;
    }
    
    .role-badge.admin {
        background: #e8f5e8;
        color: #2e7d32;
    }
    
    .filters-section {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        margin-bottom: 25px;
    }
    
    .filters-form {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        align-items: end;
    }
    
    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
    
    .filter-group label {
        font-weight: 600;
        color: #333;
        font-size: 14px;
    }
    
    .filter-group input,
    .filter-group select {
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 14px;
        transition: border-color 0.3s ease;
    }
    
    .filter-group input:focus,
    .filter-group select:focus {
        outline: none;
        border-color: #667eea;
    }
    
    .btn {
        padding: 12px 20px;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        justify-content: center;
    }
    
    .btn-primary {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }
    
    .btn-secondary {
        background: #6c757d;
        color: white;
    }
    
    .btn-secondary:hover {
        background: #5a6268;
        transform: translateY(-2px);
    }
    
    .pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
        margin-top: 30px;
        padding: 20px;
    }
    
    .page-btn {
        padding: 10px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        color: #333;
        text-decoration: none;
        transition: all 0.3s ease;
        min-width: 45px;
        text-align: center;
    }
    
    .page-btn:hover,
    .page-btn.active {
        background: #667eea;
        color: white;
        border-color: #667eea;
        transform: translateY(-2px);
    }
    
    .alert {
        padding: 15px 20px;
        border-radius: 10px;
        margin-bottom: 25px;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .alert-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #333;
    }
    
    .form-group select {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 14px;
    }
    
    .form-actions {
        display: flex;
        gap: 15px;
        justify-content: flex-end;
        margin-top: 30px;
    }
`;

// إضافة الأنماط إلى الصفحة
const styleSheet = document.createElement('style');
styleSheet.textContent = modalStyles;
document.head.appendChild(styleSheet);
