<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشغيل سريع - HubHam</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            direction: rtl;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            max-width: 800px;
            width: 90%;
            text-align: center;
        }
        
        .logo {
            font-size: 64px;
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 36px;
        }
        
        .step {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: right;
        }
        
        .step h3 {
            color: #667eea;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .step-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .btn {
            display: inline-block;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 25px;
            margin: 10px;
            transition: transform 0.3s ease;
            font-weight: bold;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .status {
            padding: 10px 20px;
            border-radius: 10px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .requirements {
            text-align: right;
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .requirements h4 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .requirements ul {
            list-style: none;
            padding: 0;
        }
        
        .requirements li {
            padding: 5px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .check {
            color: #28a745;
            font-weight: bold;
        }
        
        .cross {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">💎</div>
        <h1>مرحباً بك في HubHam</h1>
        <p>منصة التواصل الاجتماعي الجديدة</p>
        
        <?php
        // فحص المتطلبات
        $php_version = version_compare(PHP_VERSION, '7.4.0', '>=');
        $pdo_available = extension_loaded('pdo');
        $mysql_available = extension_loaded('pdo_mysql');
        $gd_available = extension_loaded('gd');
        $uploads_writable = is_writable('uploads') || mkdir('uploads', 0755, true);
        $logs_writable = is_writable('logs') || mkdir('logs', 0755, true);
        
        $all_requirements = $php_version && $pdo_available && $mysql_available && $gd_available && $uploads_writable && $logs_writable;
        ?>
        
        <div class="requirements">
            <h4>فحص المتطلبات:</h4>
            <ul>
                <li>
                    <span class="<?php echo $php_version ? 'check' : 'cross'; ?>">
                        <?php echo $php_version ? '✓' : '✗'; ?>
                    </span>
                    PHP 7.4+ (الحالي: <?php echo PHP_VERSION; ?>)
                </li>
                <li>
                    <span class="<?php echo $pdo_available ? 'check' : 'cross'; ?>">
                        <?php echo $pdo_available ? '✓' : '✗'; ?>
                    </span>
                    PDO Extension
                </li>
                <li>
                    <span class="<?php echo $mysql_available ? 'check' : 'cross'; ?>">
                        <?php echo $mysql_available ? '✓' : '✗'; ?>
                    </span>
                    MySQL PDO Driver
                </li>
                <li>
                    <span class="<?php echo $gd_available ? 'check' : 'cross'; ?>">
                        <?php echo $gd_available ? '✓' : '✗'; ?>
                    </span>
                    GD Extension (للصور)
                </li>
                <li>
                    <span class="<?php echo $uploads_writable ? 'check' : 'cross'; ?>">
                        <?php echo $uploads_writable ? '✓' : '✗'; ?>
                    </span>
                    مجلد uploads قابل للكتابة
                </li>
                <li>
                    <span class="<?php echo $logs_writable ? 'check' : 'cross'; ?>">
                        <?php echo $logs_writable ? '✓' : '✗'; ?>
                    </span>
                    مجلد logs قابل للكتابة
                </li>
            </ul>
        </div>
        
        <?php if ($all_requirements): ?>
            <div class="status success">
                ✓ جميع المتطلبات متوفرة! يمكنك المتابعة
            </div>
        <?php else: ?>
            <div class="status error">
                ✗ بعض المتطلبات غير متوفرة. يرجى إصلاحها أولاً
            </div>
        <?php endif; ?>
        
        <div class="step">
            <h3>
                <span class="step-number">1</span>
                إعداد قاعدة البيانات
            </h3>
            <p>قم بتحديث إعدادات قاعدة البيانات في ملف config/database.php ثم اضغط على الزر أدناه لإعداد الجداول</p>
            <?php if ($all_requirements): ?>
                <a href="setup.php" class="btn">إعداد قاعدة البيانات</a>
            <?php else: ?>
                <span class="btn btn-secondary" style="opacity: 0.5;">إعداد قاعدة البيانات (غير متاح)</span>
            <?php endif; ?>
        </div>
        
        <div class="step">
            <h3>
                <span class="step-number">2</span>
                تشغيل الخادم
            </h3>
            <p>استخدم أحد الملفات التالية لتشغيل الخادم على المنفذ 3322:</p>
            <div>
                <strong>Windows:</strong> انقر مرتين على start-server.bat<br>
                <strong>Linux/Mac:</strong> نفذ الأمر ./start-server.sh
            </div>
        </div>
        
        <div class="step">
            <h3>
                <span class="step-number">3</span>
                الوصول للموقع
            </h3>
            <p>بعد تشغيل الخادم، يمكنك الوصول للموقع عبر:</p>
            <a href="http://localhost:3322" class="btn" target="_blank">فتح الموقع</a>
        </div>
        
        <div class="step">
            <h3>
                <span class="step-number">4</span>
                بيانات الدخول الافتراضية
            </h3>
            <div style="background: #fff3cd; padding: 15px; border-radius: 10px; margin: 15px 0;">
                <strong>المدير العام:</strong><br>
                اسم المستخدم: admin<br>
                البريد الإلكتروني: <EMAIL><br>
                كلمة المرور: Admin@123
            </div>
        </div>
        
        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee;">
            <h3>روابط مفيدة:</h3>
            <a href="index.php" class="btn">الصفحة الرئيسية</a>
            <a href="admin/dashboard.php" class="btn">لوحة تحكم الإدارة</a>
            <a href="README.md" class="btn btn-secondary">دليل المستخدم</a>
        </div>
        
        <div style="margin-top: 30px; color: #666; font-size: 14px;">
            <p>HubHam - منصة التواصل الاجتماعي الجديدة</p>
            <p>تم التطوير بواسطة فريق HubHam</p>
        </div>
    </div>
</body>
</html>
