<?php
// اختبار الاتصال بقاعدة البيانات
require_once 'config/database.php';

echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>";

try {
    // محاولة الاتصال
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
    
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح!</p>";
    
    // اختبار الجداول
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "<p style='color: orange;'>⚠️ قاعدة البيانات فارغة - تحتاج لتشغيل setup.php</p>";
        echo "<a href='setup.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إعداد قاعدة البيانات</a>";
    } else {
        echo "<p style='color: green;'>✅ تم العثور على " . count($tables) . " جدول في قاعدة البيانات</p>";
        echo "<h3>الجداول الموجودة:</h3>";
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
        
        // اختبار جدول المستخدمين
        try {
            $userCount = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
            echo "<p>عدد المستخدمين: $userCount</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>خطأ في جدول المستخدمين: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ فشل الاتصال بقاعدة البيانات:</p>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    
    echo "<h3>تحقق من:</h3>";
    echo "<ul>";
    echo "<li>صحة بيانات الاتصال</li>";
    echo "<li>أن الخادم يعمل</li>";
    echo "<li>أن قاعدة البيانات موجودة</li>";
    echo "<li>صحة اسم المستخدم وكلمة المرور</li>";
    echo "</ul>";
}

echo "<br><a href='index.php'>العودة للصفحة الرئيسية</a>";
?>
