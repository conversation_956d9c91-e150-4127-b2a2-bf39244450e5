/* تصميم متجاوب للأجهزة المختلفة */

/* الأجهزة اللوحية الكبيرة */
@media (max-width: 1024px) {
    .content-grid {
        grid-template-columns: 200px 1fr 250px;
        gap: 20px;
    }
    
    .nav-container {
        padding: 12px 15px;
    }
    
    .nav-search {
        max-width: 300px;
        margin: 0 20px;
    }
    
    .sidebar-left, .sidebar-right {
        padding: 15px;
    }
    
    .post-composer {
        padding: 20px;
    }
}

/* الأجهزة اللوحية */
@media (max-width: 768px) {
    .content-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .sidebar-left {
        order: 3;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }
    
    .sidebar-right {
        order: 2;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
    }
    
    .main-feed {
        order: 1;
    }
    
    .nav-container {
        flex-wrap: wrap;
        gap: 15px;
    }
    
    .nav-search {
        order: 3;
        width: 100%;
        max-width: none;
        margin: 0;
    }
    
    .nav-menu {
        gap: 15px;
    }
    
    .nav-link span {
        display: none;
    }
    
    .nav-link {
        padding: 10px;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        justify-content: center;
    }
    
    .composer-header {
        flex-direction: column;
        gap: 10px;
    }
    
    .composer-avatar {
        align-self: flex-start;
    }
    
    .post-actions {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .action-btn {
        flex: 1;
        justify-content: center;
        min-width: 80px;
    }
    
    .symbols-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 10px;
    }
    
    .user-stats {
        flex-direction: column;
        gap: 15px;
    }
    
    .stat-item {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        background: rgba(102, 126, 234, 0.05);
        border-radius: 10px;
    }
}

/* الهواتف الذكية الكبيرة */
@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }
    
    .nav-container {
        padding: 10px 15px;
    }
    
    .nav-brand h1 {
        font-size: 24px;
    }
    
    .search-box input {
        padding: 10px 40px 10px 15px;
        font-size: 14px;
    }
    
    .nav-menu {
        gap: 10px;
    }
    
    .nav-link {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }
    
    .user-avatar {
        width: 35px;
        height: 35px;
    }
    
    .main-content {
        margin-top: 120px;
        padding: 15px 0;
    }
    
    .sidebar-left, .sidebar-right {
        padding: 15px;
        border-radius: 15px;
    }
    
    .sidebar-left {
        grid-template-columns: 1fr;
    }
    
    .sidebar-right {
        grid-template-columns: 1fr;
    }
    
    .sidebar-section h3 {
        font-size: 16px;
        margin-bottom: 12px;
    }
    
    .sidebar-menu a {
        padding: 10px 12px;
        font-size: 14px;
    }
    
    .post-composer {
        padding: 15px;
        border-radius: 15px;
        margin-bottom: 15px;
    }
    
    .composer-avatar {
        width: 40px;
        height: 40px;
    }
    
    .composer-header textarea {
        font-size: 16px;
        min-height: 50px;
    }
    
    .media-tools {
        gap: 10px;
    }
    
    .tool-btn {
        font-size: 18px;
        padding: 6px;
    }
    
    .post-btn {
        padding: 10px 20px;
        font-size: 14px;
    }
    
    .feed-container {
        border-radius: 15px;
    }
    
    .post-item {
        padding: 15px;
    }
    
    .post-avatar {
        width: 40px;
        height: 40px;
    }
    
    .post-user-name {
        font-size: 14px;
    }
    
    .post-username, .post-time {
        font-size: 12px;
    }
    
    .post-content {
        font-size: 15px;
        margin-bottom: 12px;
    }
    
    .post-actions {
        gap: 5px;
    }
    
    .action-btn {
        padding: 6px 10px;
        font-size: 12px;
        min-width: 70px;
    }
    
    .trending-item {
        padding: 10px 12px;
    }
    
    .trending-symbol {
        font-size: 14px;
    }
    
    .trending-count {
        font-size: 11px;
    }
    
    .modal-content {
        width: 95%;
        margin: 10px;
        border-radius: 15px;
    }
    
    .modal-header {
        padding: 15px 20px;
    }
    
    .modal-header h3 {
        font-size: 18px;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .symbols-grid {
        grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
        gap: 8px;
    }
    
    .symbol-btn {
        padding: 12px 8px;
        font-size: 12px;
    }
    
    .user-dropdown {
        min-width: 180px;
        right: -10px;
    }
    
    .user-dropdown a {
        padding: 10px 15px;
        font-size: 14px;
    }
    
    .interests-tags {
        gap: 6px;
    }
    
    .interest-tag {
        padding: 5px 10px;
        font-size: 11px;
    }
    
    .loading-spinner {
        padding: 30px;
    }
    
    .loading-spinner i {
        font-size: 24px;
        margin-bottom: 10px;
    }
}

/* الهواتف الذكية الصغيرة */
@media (max-width: 360px) {
    .nav-brand h1 {
        font-size: 20px;
    }
    
    .nav-menu {
        gap: 8px;
    }
    
    .nav-link {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }
    
    .user-avatar {
        width: 32px;
        height: 32px;
    }
    
    .main-content {
        margin-top: 130px;
    }
    
    .post-composer {
        padding: 12px;
    }
    
    .composer-avatar {
        width: 35px;
        height: 35px;
    }
    
    .composer-header textarea {
        font-size: 15px;
        min-height: 45px;
    }
    
    .tool-btn {
        font-size: 16px;
        padding: 5px;
    }
    
    .post-btn {
        padding: 8px 16px;
        font-size: 13px;
    }
    
    .post-item {
        padding: 12px;
    }
    
    .post-avatar {
        width: 35px;
        height: 35px;
    }
    
    .post-content {
        font-size: 14px;
    }
    
    .action-btn {
        padding: 5px 8px;
        font-size: 11px;
        min-width: 60px;
    }
    
    .sidebar-section {
        margin-bottom: 15px;
    }
    
    .sidebar-section h3 {
        font-size: 15px;
        margin-bottom: 10px;
    }
    
    .sidebar-menu a {
        padding: 8px 10px;
        font-size: 13px;
    }
    
    .trending-item {
        padding: 8px 10px;
    }
    
    .modal-content {
        width: 98%;
        margin: 5px;
    }
    
    .modal-header {
        padding: 12px 15px;
    }
    
    .modal-body {
        padding: 15px;
    }
    
    .symbols-grid {
        grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
        gap: 6px;
    }
    
    .symbol-btn {
        padding: 10px 6px;
        font-size: 11px;
    }
}

/* تحسينات للأجهزة ذات الدقة العالية */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .nav-brand h1, .post-user-name, .sidebar-section h3 {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* تحسينات للوضع الأفقي على الهواتف */
@media (max-height: 500px) and (orientation: landscape) {
    .main-content {
        margin-top: 70px;
    }
    
    .nav-container {
        padding: 8px 15px;
    }
    
    .content-grid {
        gap: 10px;
    }
    
    .sidebar-left, .sidebar-right {
        padding: 10px;
    }
    
    .post-composer {
        padding: 10px;
        margin-bottom: 10px;
    }
    
    .post-item {
        padding: 10px;
    }
}

/* تحسينات للطباعة */
@media print {
    .navbar, .sidebar-left, .sidebar-right, .post-composer, .post-actions {
        display: none !important;
    }
    
    .content-grid {
        grid-template-columns: 1fr;
    }
    
    .main-feed {
        background: white !important;
        box-shadow: none !important;
    }
    
    .post-item {
        border-bottom: 1px solid #ccc !important;
        break-inside: avoid;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}

/* تحسينات لإمكانية الوصول */
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* تحسينات للوضع المظلم (إذا كان مدعوماً) */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --text-color: #ffffff;
        --border-color: #333333;
    }
}

/* تحسينات للشاشات الكبيرة جداً */
@media (min-width: 1400px) {
    .container {
        max-width: 1400px;
    }
    
    .content-grid {
        grid-template-columns: 300px 1fr 350px;
        gap: 40px;
    }
    
    .nav-container {
        max-width: 1400px;
    }
    
    .sidebar-left, .sidebar-right {
        padding: 25px;
    }
    
    .post-composer {
        padding: 30px;
    }
    
    .post-item {
        padding: 30px;
    }
}
