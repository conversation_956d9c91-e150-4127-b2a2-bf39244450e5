<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>تشخيص المشاكل - HubHam</h1>";

// فحص PHP
echo "<h2>معلومات PHP:</h2>";
echo "إصدار PHP: " . PHP_VERSION . "<br>";
echo "الوقت الحالي: " . date('Y-m-d H:i:s') . "<br>";

// فحص الإضافات
echo "<h2>الإضافات المطلوبة:</h2>";
$extensions = ['pdo', 'pdo_mysql', 'gd', 'json', 'mbstring'];
foreach ($extensions as $ext) {
    $status = extension_loaded($ext) ? '✅' : '❌';
    echo "$status $ext<br>";
}

// فحص الملفات
echo "<h2>الملفات الأساسية:</h2>";
$files = [
    'config/database.php',
    'includes/functions.php',
    'auth/login.php',
    'admin/dashboard.php'
];

foreach ($files as $file) {
    $status = file_exists($file) ? '✅' : '❌';
    echo "$status $file<br>";
}

// فحص المجلدات
echo "<h2>المجلدات:</h2>";
$dirs = ['uploads', 'logs', 'assets', 'api'];
foreach ($dirs as $dir) {
    $exists = is_dir($dir) ? '✅' : '❌';
    $writable = is_writable($dir) ? '(قابل للكتابة)' : '(غير قابل للكتابة)';
    echo "$exists $dir $writable<br>";
}

// اختبار قاعدة البيانات
echo "<h2>اختبار قاعدة البيانات:</h2>";
try {
    require_once 'config/database.php';
    
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "✅ الاتصال بقاعدة البيانات نجح<br>";
    
    // فحص الجداول
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "عدد الجداول: " . count($tables) . "<br>";
    
    // فحص جدول المستخدمين
    if (in_array('users', $tables)) {
        $userCount = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
        echo "عدد المستخدمين: $userCount<br>";
    } else {
        echo "❌ جدول المستخدمين غير موجود<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "<br>";
}

// اختبار الدوال
echo "<h2>اختبار الدوال:</h2>";
if (file_exists('includes/functions.php')) {
    try {
        require_once 'includes/functions.php';
        echo "✅ تم تحميل ملف الدوال<br>";
        
        // اختبار دالة بسيطة
        if (function_exists('sanitizeInput')) {
            echo "✅ دالة sanitizeInput موجودة<br>";
        } else {
            echo "❌ دالة sanitizeInput غير موجودة<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ خطأ في تحميل الدوال: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ ملف الدوال غير موجود<br>";
}

echo "<br><a href='index.php'>اختبار الصفحة الرئيسية</a><br>";
echo "<a href='auth/login.php'>اختبار صفحة تسجيل الدخول</a><br>";
echo "<a href='setup-hubham.php'>إعداد قاعدة البيانات</a><br>";
?>
