<?php
require_once __DIR__ . '/../config/database.php';

// دوال المستخدمين
function getUserById($user_id) {
    $sql = "SELECT * FROM users WHERE id = ? AND status != 'banned'";
    return fetchOne($sql, [$user_id]);
}

function getUserByUsername($username) {
    $sql = "SELECT * FROM users WHERE username = ? AND status != 'banned'";
    return fetchOne($sql, [$username]);
}

function getUserByEmail($email) {
    $sql = "SELECT * FROM users WHERE email = ? AND status != 'banned'";
    return fetchOne($sql, [$email]);
}

function createUser($data) {
    $sql = "INSERT INTO users (username, email, password_hash, display_name, location, birth_date, gender, email_verification_token) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $data['username'],
        $data['email'],
        hashPassword($data['password']),
        $data['display_name'],
        $data['location'] ?? null,
        $data['birth_date'] ?? null,
        $data['gender'] ?? null,
        generateSecureToken()
    ];
    
    if (executeQuery($sql, $params)) {
        $user_id = getLastInsertId();
        logActivity($user_id, 'user_registered', 'New user registration');
        return $user_id;
    }
    return false;
}

function updateUserProfile($user_id, $data) {
    $allowed_fields = ['display_name', 'bio', 'location', 'website', 'birth_date', 'gender'];
    $updates = [];
    $params = [];
    
    foreach ($allowed_fields as $field) {
        if (isset($data[$field])) {
            $updates[] = "$field = ?";
            $params[] = $data[$field];
        }
    }
    
    if (empty($updates)) return false;
    
    $params[] = $user_id;
    $sql = "UPDATE users SET " . implode(', ', $updates) . ", updated_at = NOW() WHERE id = ?";
    
    if (executeQuery($sql, $params)) {
        logActivity($user_id, 'profile_updated', 'Profile information updated');
        return true;
    }
    return false;
}

function updateUserEmail($user_id, $new_email) {
    $user = getUserById($user_id);
    if (!$user) return false;
    
    // التحقق من عدد مرات تغيير الإيميل
    if ($user['email_changes_count'] >= MAX_EMAIL_CHANGES) {
        return ['error' => 'تم تجاوز الحد الأقصى لتغيير البريد الإلكتروني'];
    }
    
    // التحقق من عدم وجود الإيميل الجديد
    if (getUserByEmail($new_email)) {
        return ['error' => 'البريد الإلكتروني مستخدم بالفعل'];
    }
    
    beginTransaction();
    try {
        // حفظ الإيميل القديم في التاريخ
        $sql = "INSERT INTO email_history (user_id, old_email, new_email, ip_address) VALUES (?, ?, ?, ?)";
        executeQuery($sql, [$user_id, $user['email'], $new_email, $_SERVER['REMOTE_ADDR']]);
        
        // تحديث الإيميل
        $sql = "UPDATE users SET email = ?, email_changes_count = email_changes_count + 1, 
                last_email_change = NOW(), email_verified = FALSE, email_verification_token = ? 
                WHERE id = ?";
        executeQuery($sql, [$new_email, generateSecureToken(), $user_id]);
        
        commit();
        logActivity($user_id, 'email_changed', "Email changed from {$user['email']} to $new_email");
        return ['success' => true, 'verification_required' => true];
    } catch (Exception $e) {
        rollback();
        return ['error' => 'حدث خطأ أثناء تحديث البريد الإلكتروني'];
    }
}

function verifyEmail($token) {
    $sql = "UPDATE users SET email_verified = TRUE, email_verification_token = NULL, status = 'active' 
            WHERE email_verification_token = ? AND status = 'pending'";
    
    if (executeQuery($sql, [$token])) {
        return true;
    }
    return false;
}

// دوال المنشورات
function createPost($user_id, $content, $media_data = null, $symbols = null, $location = null) {
    $sql = "INSERT INTO posts (user_id, content, media_type, media_url, media_thumbnail, symbols, location) 
            VALUES (?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $user_id,
        $content,
        $media_data['type'] ?? 'none',
        $media_data['url'] ?? null,
        $media_data['thumbnail'] ?? null,
        $symbols ? json_encode($symbols) : null,
        $location
    ];
    
    if (executeQuery($sql, $params)) {
        $post_id = getLastInsertId();
        
        // تحديث الترندات إذا كانت هناك رموز
        if ($symbols) {
            updateTrending($symbols);
        }
        
        logActivity($user_id, 'post_created', "Post ID: $post_id");
        return $post_id;
    }
    return false;
}

function getPosts($user_id = null, $limit = 20, $offset = 0, $type = 'timeline') {
    $where_conditions = ["p.is_deleted = FALSE"];
    $params = [];
    
    if ($user_id && $type === 'user_posts') {
        $where_conditions[] = "p.user_id = ?";
        $params[] = $user_id;
    } elseif ($user_id && $type === 'timeline') {
        // عرض منشورات المستخدم والأشخاص الذين يتابعهم
        $where_conditions[] = "(p.user_id = ? OR p.user_id IN (
            SELECT following_id FROM follows WHERE follower_id = ? AND status = 'accepted'
        ))";
        $params[] = $user_id;
        $params[] = $user_id;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $sql = "SELECT p.*, u.username, u.display_name, u.profile_image, u.role,
                   (SELECT COUNT(*) FROM likes l WHERE l.post_id = p.id) as likes_count,
                   (SELECT COUNT(*) FROM comments c WHERE c.post_id = p.id AND c.is_deleted = FALSE) as comments_count,
                   " . ($user_id ? "(SELECT COUNT(*) FROM likes l WHERE l.post_id = p.id AND l.user_id = ?) as user_liked" : "0 as user_liked") . "
            FROM posts p 
            JOIN users u ON p.user_id = u.id 
            WHERE $where_clause 
            ORDER BY p.created_at DESC 
            LIMIT ? OFFSET ?";
    
    if ($user_id && $type !== 'user_posts') {
        $params[] = $user_id;
    }
    $params[] = $limit;
    $params[] = $offset;
    
    return fetchAll($sql, $params);
}

function getPostById($post_id, $user_id = null) {
    $sql = "SELECT p.*, u.username, u.display_name, u.profile_image, u.role,
                   (SELECT COUNT(*) FROM likes l WHERE l.post_id = p.id) as likes_count,
                   (SELECT COUNT(*) FROM comments c WHERE c.post_id = p.id AND c.is_deleted = FALSE) as comments_count,
                   " . ($user_id ? "(SELECT COUNT(*) FROM likes l WHERE l.post_id = p.id AND l.user_id = ?) as user_liked" : "0 as user_liked") . "
            FROM posts p 
            JOIN users u ON p.user_id = u.id 
            WHERE p.id = ? AND p.is_deleted = FALSE";
    
    $params = $user_id ? [$user_id, $post_id] : [$post_id];
    return fetchOne($sql, $params);
}

function likePost($user_id, $post_id) {
    // التحقق من وجود الإعجاب
    $existing = fetchOne("SELECT id FROM likes WHERE user_id = ? AND post_id = ?", [$user_id, $post_id]);
    
    if ($existing) {
        // إلغاء الإعجاب
        executeQuery("DELETE FROM likes WHERE user_id = ? AND post_id = ?", [$user_id, $post_id]);
        executeQuery("UPDATE posts SET likes_count = likes_count - 1 WHERE id = ?", [$post_id]);
        return ['action' => 'unliked'];
    } else {
        // إضافة إعجاب
        executeQuery("INSERT INTO likes (user_id, post_id) VALUES (?, ?)", [$user_id, $post_id]);
        executeQuery("UPDATE posts SET likes_count = likes_count + 1 WHERE id = ?", [$post_id]);
        
        // إنشاء إشعار
        $post = getPostById($post_id);
        if ($post && $post['user_id'] != $user_id) {
            createNotification($post['user_id'], 'like', 'أعجب بمنشورك', null, $user_id, $post_id);
        }
        
        return ['action' => 'liked'];
    }
}

// دوال التعليقات
function createComment($user_id, $post_id, $content, $parent_id = null) {
    $sql = "INSERT INTO comments (user_id, post_id, parent_id, content) VALUES (?, ?, ?, ?)";
    
    if (executeQuery($sql, [$user_id, $post_id, $parent_id, $content])) {
        $comment_id = getLastInsertId();
        
        // تحديث عداد التعليقات
        executeQuery("UPDATE posts SET comments_count = comments_count + 1 WHERE id = ?", [$post_id]);
        
        // إنشاء إشعار
        $post = getPostById($post_id);
        if ($post && $post['user_id'] != $user_id) {
            createNotification($post['user_id'], 'comment', 'علق على منشورك', $content, $user_id, $post_id, $comment_id);
        }
        
        logActivity($user_id, 'comment_created', "Comment ID: $comment_id on Post ID: $post_id");
        return $comment_id;
    }
    return false;
}

function getComments($post_id, $limit = 20, $offset = 0) {
    $sql = "SELECT c.*, u.username, u.display_name, u.profile_image, u.role,
                   (SELECT COUNT(*) FROM likes l WHERE l.post_id = c.id) as likes_count
            FROM comments c 
            JOIN users u ON c.user_id = u.id 
            WHERE c.post_id = ? AND c.is_deleted = FALSE 
            ORDER BY c.created_at ASC 
            LIMIT ? OFFSET ?";
    
    return fetchAll($sql, [$post_id, $limit, $offset]);
}

// دوال المتابعة
function followUser($follower_id, $following_id) {
    if ($follower_id == $following_id) return false;
    
    // التحقق من وجود المتابعة
    $existing = fetchOne("SELECT id, status FROM follows WHERE follower_id = ? AND following_id = ?", 
                        [$follower_id, $following_id]);
    
    if ($existing) {
        if ($existing['status'] === 'accepted') {
            // إلغاء المتابعة
            executeQuery("DELETE FROM follows WHERE follower_id = ? AND following_id = ?", 
                        [$follower_id, $following_id]);
            return ['action' => 'unfollowed'];
        }
    } else {
        // إضافة متابعة جديدة
        executeQuery("INSERT INTO follows (follower_id, following_id) VALUES (?, ?)", 
                    [$follower_id, $following_id]);
        
        // إنشاء إشعار
        createNotification($following_id, 'follow', 'بدأ في متابعتك', null, $follower_id);
        
        return ['action' => 'followed'];
    }
    
    return false;
}

function getFollowers($user_id, $limit = 20, $offset = 0) {
    $sql = "SELECT u.id, u.username, u.display_name, u.profile_image, u.role, f.created_at
            FROM follows f 
            JOIN users u ON f.follower_id = u.id 
            WHERE f.following_id = ? AND f.status = 'accepted' 
            ORDER BY f.created_at DESC 
            LIMIT ? OFFSET ?";
    
    return fetchAll($sql, [$user_id, $limit, $offset]);
}

function getFollowing($user_id, $limit = 20, $offset = 0) {
    $sql = "SELECT u.id, u.username, u.display_name, u.profile_image, u.role, f.created_at
            FROM follows f 
            JOIN users u ON f.following_id = u.id 
            WHERE f.follower_id = ? AND f.status = 'accepted' 
            ORDER BY f.created_at DESC 
            LIMIT ? OFFSET ?";
    
    return fetchAll($sql, [$user_id, $limit, $offset]);
}

// دوال الإشعارات
function createNotification($user_id, $type, $title, $content = null, $related_user_id = null, 
                           $related_post_id = null, $related_comment_id = null) {
    $sql = "INSERT INTO notifications (user_id, type, title, content, related_user_id, related_post_id, related_comment_id) 
            VALUES (?, ?, ?, ?, ?, ?, ?)";
    
    return executeQuery($sql, [$user_id, $type, $title, $content, $related_user_id, $related_post_id, $related_comment_id]);
}

function getNotifications($user_id, $limit = 20, $offset = 0) {
    $sql = "SELECT n.*, u.username, u.display_name, u.profile_image
            FROM notifications n 
            LEFT JOIN users u ON n.related_user_id = u.id 
            WHERE n.user_id = ? 
            ORDER BY n.created_at DESC 
            LIMIT ? OFFSET ?";
    
    return fetchAll($sql, [$user_id, $limit, $offset]);
}

function markNotificationAsRead($notification_id, $user_id) {
    $sql = "UPDATE notifications SET is_read = TRUE WHERE id = ? AND user_id = ?";
    return executeQuery($sql, [$notification_id, $user_id]);
}

// دوال الترندات
function updateTrending($symbols) {
    foreach ($symbols as $symbol) {
        $sql = "INSERT INTO trending (symbol, posts_count, engagement_score) 
                VALUES (?, 1, 1) 
                ON DUPLICATE KEY UPDATE 
                posts_count = posts_count + 1, 
                engagement_score = engagement_score + 1,
                last_updated = NOW()";
        executeQuery($sql, [$symbol]);
    }
}

function getTrending($limit = 10) {
    $hours_ago = date('Y-m-d H:i:s', strtotime('-' . TRENDING_REFRESH_HOURS . ' hours'));
    
    $sql = "SELECT symbol, posts_count, engagement_score 
            FROM trending 
            WHERE last_updated >= ? 
            ORDER BY engagement_score DESC, posts_count DESC 
            LIMIT ?";
    
    return fetchAll($sql, [$hours_ago, $limit]);
}

// دوال البحث
function searchUsers($query, $limit = 20, $offset = 0) {
    $search_term = "%$query%";
    $sql = "SELECT id, username, display_name, profile_image, bio, role
            FROM users 
            WHERE (username LIKE ? OR display_name LIKE ?) AND status = 'active'
            ORDER BY 
                CASE WHEN username = ? THEN 1 ELSE 2 END,
                CASE WHEN username LIKE ? THEN 1 ELSE 2 END,
                display_name
            LIMIT ? OFFSET ?";
    
    return fetchAll($sql, [$search_term, $search_term, $query, "$query%", $limit, $offset]);
}

function searchPosts($query, $limit = 20, $offset = 0) {
    $search_term = "%$query%";
    $sql = "SELECT p.*, u.username, u.display_name, u.profile_image, u.role
            FROM posts p 
            JOIN users u ON p.user_id = u.id 
            WHERE p.content LIKE ? AND p.is_deleted = FALSE AND u.status = 'active'
            ORDER BY p.created_at DESC 
            LIMIT ? OFFSET ?";
    
    return fetchAll($sql, [$search_term, $limit, $offset]);
}

// دوال الأمان
function isBlocked($user_id, $target_user_id) {
    $sql = "SELECT id FROM blocks WHERE (blocker_id = ? AND blocked_id = ?) OR (blocker_id = ? AND blocked_id = ?)";
    return fetchOne($sql, [$user_id, $target_user_id, $target_user_id, $user_id]) !== false;
}

function blockUser($blocker_id, $blocked_id, $reason = '') {
    if ($blocker_id == $blocked_id) return false;
    
    $sql = "INSERT INTO blocks (blocker_id, blocked_id, reason) VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE reason = VALUES(reason)";
    
    if (executeQuery($sql, [$blocker_id, $blocked_id, $reason])) {
        // إزالة المتابعة المتبادلة
        executeQuery("DELETE FROM follows WHERE (follower_id = ? AND following_id = ?) OR (follower_id = ? AND following_id = ?)",
                    [$blocker_id, $blocked_id, $blocked_id, $blocker_id]);
        
        logActivity($blocker_id, 'user_blocked', "Blocked user ID: $blocked_id");
        return true;
    }
    return false;
}

// دوال التحقق من الصلاحيات
function hasPermission($user, $permission) {
    $permissions = [
        'admin' => ['manage_users', 'manage_posts', 'manage_reports', 'manage_system', 'view_admin_panel'],
        'moderator' => ['manage_posts', 'manage_reports', 'view_admin_panel'],
        'user' => []
    ];
    
    return in_array($permission, $permissions[$user['role']] ?? []);
}

function requirePermission($user, $permission) {
    if (!hasPermission($user, $permission)) {
        http_response_code(403);
        die(json_encode(['error' => 'ليس لديك صلاحية للوصول إلى هذه الصفحة']));
    }
}

// دوال مساعدة أخرى
function formatTimeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'الآن';
    if ($time < 3600) return floor($time/60) . ' د';
    if ($time < 86400) return floor($time/3600) . ' س';
    if ($time < 2592000) return floor($time/86400) . ' ي';
    if ($time < 31536000) return floor($time/2592000) . ' ش';
    
    return floor($time/31536000) . ' سنة';
}

function generateConversationId($user1_id, $user2_id) {
    $ids = [$user1_id, $user2_id];
    sort($ids);
    return 'conv_' . implode('_', $ids);
}

function uploadFile($file, $type = 'image') {
    $allowed_types = $type === 'image' ? ALLOWED_IMAGE_TYPES : ALLOWED_VIDEO_TYPES;
    $upload_dir = "uploads/{$type}s/";
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['error' => 'خطأ في رفع الملف'];
    }
    
    if ($file['size'] > MAX_FILE_SIZE) {
        return ['error' => 'حجم الملف كبير جداً'];
    }
    
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($file_extension, $allowed_types)) {
        return ['error' => 'نوع الملف غير مدعوم'];
    }
    
    $filename = uniqid() . '_' . time() . '.' . $file_extension;
    $filepath = $upload_dir . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return ['success' => true, 'filename' => $filename, 'path' => $filepath];
    }
    
    return ['error' => 'فشل في حفظ الملف'];
}
?>
